{"manifestVersion": "1.0.0", "name": "SmartCodeAssistant", "displayName": "智能代码助手", "version": "1.0.0", "description": "AI驱动的全方位代码开发助手，支持代码分析、优化建议、自动测试生成、文档生成、安全扫描等功能。支持多种编程语言，提供专业级的代码质量保证。", "author": "VCP Advanced", "pluginType": "synchronous", "entryPoint": {"type": "nodejs", "command": "node SmartCodeAssistant.js"}, "communication": {"protocol": "stdio", "timeout": 180000}, "configSchema": {"DebugMode": {"type": "boolean", "description": "是否启用详细的调试日志输出到stderr", "default": false, "required": false}}, "capabilities": {"invocationCommands": [{"command": "analyze", "description": "深度分析代码质量、性能和安全性。\n\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」SmartCodeAssistant「末」,\ncommand:「始」analyze「末」,\ncode:「始」(必需) 要分析的代码内容「末」,\nlanguage:「始」(可选) 编程语言，如python、javascript、java等「末」,\nanalysis_type:「始」(可选) 分析类型：quality(质量)、performance(性能)、security(安全)、all(全部)。默认：all「末」\n<<<[END_TOOL_REQUEST]>>>\n\n功能：全面分析代码质量，包括代码规范、性能问题、安全漏洞等，提供详细的改进建议。"}, {"command": "generate_tests", "description": "自动生成单元测试代码。\n\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」SmartCodeAssistant「末」,\ncommand:「始」generate_tests「末」,\ncode:「始」(必需) 要生成测试的代码「末」,\nlanguage:「始」(必需) 编程语言「末」,\ntest_framework:「始」(可选) 测试框架，如jest、pytest、junit等「末」,\ncoverage_level:「始」(可选) 覆盖级别：basic、comprehensive、edge_cases。默认：comprehensive「末」\n<<<[END_TOOL_REQUEST]>>>\n\n功能：基于代码自动生成完整的单元测试，支持多种测试框架和覆盖级别。"}, {"command": "optimize", "description": "优化代码性能和可读性。\n\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」SmartCodeAssistant「末」,\ncommand:「始」optimize「末」,\ncode:「始」(必需) 要优化的代码「末」,\nlanguage:「始」(必需) 编程语言「末」,\noptimization_focus:「始」(可选) 优化重点：performance(性能)、readability(可读性)、memory(内存)、all(全部)。默认：all「末」\n<<<[END_TOOL_REQUEST]>>>\n\n功能：智能优化代码，提升性能和可读性，提供优化建议和重构方案。"}, {"command": "document", "description": "自动生成代码文档和注释。\n\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」SmartCodeAssistant「末」,\ncommand:「始」document「末」,\ncode:「始」(必需) 要生成文档的代码「末」,\nlanguage:「始」(必需) 编程语言「末」,\ndoc_style:「始」(可选) 文档风格：javadoc、sphinx、jsdoc、google等「末」,\ninclude_examples:「始」(可选) 是否包含使用示例，true/false。默认：true「末」\n<<<[END_TOOL_REQUEST]>>>\n\n功能：自动生成API文档、代码注释和使用示例。"}, {"command": "security_scan", "description": "扫描代码安全漏洞和风险。\n\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」SmartCodeAssistant「末」,\ncommand:「始」security_scan「末」,\ncode:「始」(必需) 要扫描的代码「末」,\nlanguage:「始」(必需) 编程语言「末」,\nscan_level:「始」(可选) 扫描级别：basic、standard、strict。默认：standard「末」\n<<<[END_TOOL_REQUEST]>>>\n\n功能：全面扫描代码安全漏洞，识别潜在风险并提供修复建议。"}, {"command": "refactor", "description": "重构代码结构和设计模式。\n\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」SmartCodeAssistant「末」,\ncommand:「始」refactor「末」,\ncode:「始」(必需) 要重构的代码「末」,\nlanguage:「始」(必需) 编程语言「末」,\nrefactor_type:「始」(可选) 重构类型：extract_method、rename、move_class、design_pattern等「末」,\ntarget_pattern:「始」(可选) 目标设计模式，如singleton、factory、observer等「末」\n<<<[END_TOOL_REQUEST]>>>\n\n功能：智能重构代码，改善代码结构，应用设计模式。"}]}}