#!/usr/bin/env node
/**
 * SmartReminder 插件测试文件
 */

const { spawn } = require('child_process');
const path = require('path');

// 测试用例
const testCases = [
    {
        name: '创建简单提醒',
        input: {
            command: 'create',
            reminder_text: '测试提醒：喝水',
            time_expression: '5分钟后',
            repeat_type: 'once',
            notification_methods: 'websocket'
        }
    },
    {
        name: '创建明天的提醒',
        input: {
            command: 'create',
            reminder_text: '明天的会议提醒',
            time_expression: '明天上午10点',
            repeat_type: 'once',
            notification_methods: 'websocket'
        }
    },
    {
        name: '查看所有提醒',
        input: {
            command: 'list'
        }
    },
    {
        name: '搜索提醒',
        input: {
            command: 'search',
            keyword: '会议'
        }
    }
];

async function runTest(testCase) {
    return new Promise((resolve, reject) => {
        console.log(`\n🧪 测试: ${testCase.name}`);
        console.log(`📥 输入:`, JSON.stringify(testCase.input, null, 2));
        
        const child = spawn('node', ['smart_reminder.js'], {
            cwd: __dirname,
            stdio: ['pipe', 'pipe', 'pipe']
        });

        let output = '';
        let error = '';

        child.stdout.on('data', (data) => {
            output += data.toString();
        });

        child.stderr.on('data', (data) => {
            error += data.toString();
        });

        child.on('close', (code) => {
            if (code === 0) {
                try {
                    const result = JSON.parse(output);
                    console.log(`📤 输出:`, JSON.stringify(result, null, 2));
                    console.log(`✅ 测试通过`);
                    resolve(result);
                } catch (parseError) {
                    console.log(`❌ JSON解析失败:`, parseError.message);
                    console.log(`原始输出:`, output);
                    reject(parseError);
                }
            } else {
                console.log(`❌ 进程退出码: ${code}`);
                console.log(`错误输出:`, error);
                reject(new Error(`Process exited with code ${code}`));
            }
        });

        // 发送测试数据
        child.stdin.write(JSON.stringify(testCase.input));
        child.stdin.end();
    });
}

async function runAllTests() {
    console.log('🚀 开始 SmartReminder 插件测试\n');
    
    let passedTests = 0;
    let failedTests = 0;

    for (const testCase of testCases) {
        try {
            await runTest(testCase);
            passedTests++;
        } catch (error) {
            console.log(`❌ 测试失败:`, error.message);
            failedTests++;
        }
        
        // 添加延迟避免测试冲突
        await new Promise(resolve => setTimeout(resolve, 1000));
    }

    console.log(`\n📊 测试结果:`);
    console.log(`✅ 通过: ${passedTests}`);
    console.log(`❌ 失败: ${failedTests}`);
    console.log(`📈 成功率: ${((passedTests / (passedTests + failedTests)) * 100).toFixed(1)}%`);

    if (failedTests === 0) {
        console.log(`\n🎉 所有测试通过！插件可以正常使用。`);
    } else {
        console.log(`\n⚠️  有 ${failedTests} 个测试失败，请检查配置和依赖。`);
    }
}

// 检查依赖
function checkDependencies() {
    console.log('🔍 检查依赖...');
    
    const requiredModules = ['moment', 'node-schedule', 'chrono-node'];
    const missingModules = [];

    for (const module of requiredModules) {
        try {
            require(module);
            console.log(`✅ ${module} - 已安装`);
        } catch (error) {
            console.log(`❌ ${module} - 未安装`);
            missingModules.push(module);
        }
    }

    if (missingModules.length > 0) {
        console.log(`\n⚠️  缺少依赖模块: ${missingModules.join(', ')}`);
        console.log(`请运行: npm install`);
        return false;
    }

    console.log(`✅ 所有依赖已安装\n`);
    return true;
}

// 主函数
async function main() {
    console.log('🔧 SmartReminder 插件测试工具\n');
    
    if (!checkDependencies()) {
        process.exit(1);
    }

    await runAllTests();
}

if (require.main === module) {
    main().catch(console.error);
}
