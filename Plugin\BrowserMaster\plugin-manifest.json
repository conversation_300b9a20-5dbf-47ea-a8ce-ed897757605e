{"manifestVersion": "1.0.0", "name": "BrowserMaster", "displayName": "浏览器大师", "version": "1.0.0", "description": "统一的浏览器控制接口，整合ChromeControl、ChromeObserver和高级自动化功能。提供一站式浏览器操作解决方案。", "author": "VCP Integration Team", "pluginType": "synchronous", "entryPoint": {"type": "nodejs", "command": "node BrowserMaster.js"}, "communication": {"protocol": "stdio", "timeout": 180000}, "dependencies": ["ChromeControl", "ChromeObserver"], "capabilities": {"invocationCommands": [{"commandIdentifier": "UnifiedBrowserControl", "description": "统一的浏览器控制接口，自动选择最适合的底层插件执行操作。\n\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」BrowserMaster「末」,\naction:「始」(必需) 操作类型：basic_control(基础控制)、smart_automation(智能自动化)、page_analysis(页面分析)「末」,\noperation:「始」(必需) 具体操作「末」,\nparameters:「始」(可选) 操作参数，JSON格式「末」\n<<<[END_TOOL_REQUEST]>>>", "example": "```text\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」BrowserMaster「末」,\naction:「始」smart_automation「末」,\noperation:「始」fill_and_submit_form「末」,\nparameters:「始」{\"form_data\": {\"name\": \"张三\", \"email\": \"<EMAIL>\"}}「末」\n<<<[END_TOOL_REQUEST]>>>\n```"}, {"commandIdentifier": "SmartWorkflow", "description": "智能工作流执行，结合页面观察和精确控制。\n\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」BrowserMaster「末」,\naction:「始」smart_workflow「末」,\ngoal:「始」(必需) 工作流目标的自然语言描述「末」,\nsteps:「始」(可选) 预定义步骤，JSON数组「末」,\nauto_adapt:「始」(可选) 是否自动适应页面变化，true/false。默认：true「末」\n<<<[END_TOOL_REQUEST]>>>", "example": "```text\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」BrowserMaster「末」,\naction:「始」smart_workflow「末」,\ngoal:「始」在淘宝搜索iPhone 15并找到价格最低的商品\"末」,\nauto_adapt:「始」true「末」\n<<<[END_TOOL_REQUEST]>>>\n```"}]}}