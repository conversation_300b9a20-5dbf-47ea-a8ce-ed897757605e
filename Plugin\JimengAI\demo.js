#!/usr/bin/env node

/**
 * 即梦AI插件演示脚本
 * 展示插件的各种功能和调用方式
 */

const { spawn } = require('child_process');
const path = require('path');

// 演示用例
const demos = [
    {
        name: "🎨 创意图片生成",
        description: "生成一幅充满想象力的艺术作品",
        input: {
            type: "image",
            prompt: "梦幻森林中的发光蘑菇，夜晚，星空，魔法光芒，高清数字艺术",
            quality: "2k",
            style: "artistic",
            aspect_ratio: "16:9"
        }
    },
    {
        name: "📸 写实摄影风格",
        description: "生成专业摄影风格的图片",
        input: {
            type: "image",
            prompt: "现代都市街景，黄昏时分，霓虹灯闪烁，专业摄影，高清",
            quality: "hd",
            style: "realistic",
            aspect_ratio: "4:3"
        }
    },
    {
        name: "🎬 电影级视频",
        description: "生成电影级别的视频内容",
        input: {
            type: "video",
            prompt: "壮观的山脉日出，云海翻滚，金色阳光穿透云层",
            duration: 15,
            motion: "zoom_in",
            quality: "hd"
        }
    },
    {
        name: "🎭 专业数字人",
        description: "创建专业的数字人形象",
        input: {
            type: "digital_human",
            character: "中年男性，商务正装，自信专业，企业高管形象",
            action: "握手致意",
            expression: "友善微笑",
            background: "现代办公室会议室"
        }
    },
    {
        name: "🌟 创意数字人",
        description: "创建富有创意的数字人角色",
        input: {
            type: "digital_human",
            character: "年轻艺术家，休闲装扮，充满活力，创意工作者",
            action: "展示作品",
            expression: "兴奋激动",
            background: "艺术工作室"
        }
    }
];

// 运行演示
async function runDemo(demo) {
    return new Promise((resolve, reject) => {
        console.log(`\n🎪 ${demo.name}`);
        console.log(`📝 ${demo.description}`);
        console.log(`⚙️  参数配置:`);
        console.log(JSON.stringify(demo.input, null, 2));
        console.log(`\n🚀 开始生成...`);

        const pluginPath = path.join(__dirname, 'JimengAI.js');
        const child = spawn('node', [pluginPath], {
            stdio: ['pipe', 'pipe', 'pipe']
        });

        let output = '';
        let error = '';

        child.stdout.on('data', (data) => {
            output += data.toString();
        });

        child.stderr.on('data', (data) => {
            error += data.toString();
        });

        child.on('close', (code) => {
            try {
                if (output) {
                    // 尝试从输出中提取JSON部分
                    const lines = output.split('\n');
                    let jsonStart = -1;
                    let jsonEnd = -1;
                    
                    for (let i = 0; i < lines.length; i++) {
                        if (lines[i].trim().startsWith('{')) {
                            jsonStart = i;
                            break;
                        }
                    }
                    
                    for (let i = lines.length - 1; i >= 0; i--) {
                        if (lines[i].trim().endsWith('}')) {
                            jsonEnd = i;
                            break;
                        }
                    }
                    
                    if (jsonStart !== -1 && jsonEnd !== -1) {
                        const jsonStr = lines.slice(jsonStart, jsonEnd + 1).join('\n');
                        const result = JSON.parse(jsonStr);
                        
                        if (result.status === 'success') {
                            console.log(`✅ 生成成功！`);
                            console.log(`📊 结果概要:`);
                            console.log(`   类型: ${result.result.type}`);
                            console.log(`   模型: ${result.result.data.model || 'jimeng-3.0'}`);
                            
                            if (result.result.data.images) {
                                console.log(`   图片数量: ${result.result.data.images.length}`);
                            }
                            if (result.result.data.video_url) {
                                console.log(`   视频URL: ${result.result.data.video_url}`);
                            }
                            
                            console.log(`💬 AI反馈:`);
                            console.log(`   ${result.messageForAI}`);
                        } else {
                            console.log(`❌ 生成失败: ${result.error}`);
                        }
                    } else {
                        console.log(`⚠️  无法解析输出结果`);
                    }
                }
                
                resolve({ demo, code, output, error });
            } catch (e) {
                console.log(`❌ 处理结果时出错: ${e.message}`);
                resolve({ demo, code, output, error, parseError: e.message });
            }
        });

        child.on('error', (err) => {
            console.log(`❌ 进程启动失败: ${err.message}`);
            reject(err);
        });

        // 发送演示数据
        child.stdin.write(JSON.stringify(demo.input));
        child.stdin.end();
    });
}

// 主演示函数
async function runAllDemos() {
    console.log('🎭 即梦AI插件功能演示');
    console.log('=' * 60);
    console.log('本演示将展示即梦AI插件的各种功能');
    console.log('当前运行在模拟模式下，展示完整的功能流程\n');

    const results = [];

    for (let i = 0; i < demos.length; i++) {
        const demo = demos[i];
        
        try {
            const result = await runDemo(demo);
            results.push(result);
            
            // 演示间隔
            if (i < demos.length - 1) {
                console.log('\n⏳ 等待3秒后继续下一个演示...');
                await new Promise(resolve => setTimeout(resolve, 3000));
            }
        } catch (error) {
            console.log(`❌ 演示失败: ${demo.name} - ${error.message}`);
            results.push({ demo, error });
        }
    }

    // 输出演示总结
    console.log('\n' + '=' * 60);
    console.log('📊 演示总结:');
    
    let successCount = 0;
    let failCount = 0;

    results.forEach((result, index) => {
        const demoName = result.demo.name;
        if (result.code === 0 && result.output && !result.parseError) {
            console.log(`✅ ${index + 1}. ${demoName} - 成功`);
            successCount++;
        } else {
            console.log(`❌ ${index + 1}. ${demoName} - 失败`);
            failCount++;
        }
    });

    console.log(`\n📈 演示结果: ${successCount} 成功, ${failCount} 失败, 总计 ${results.length} 个演示`);
    
    if (failCount === 0) {
        console.log('🎉 所有演示成功完成！');
        console.log('\n💡 提示:');
        console.log('- 当前为模拟模式，展示了完整的功能流程');
        console.log('- 要使用真实API，请参考 "真实API使用指南.md"');
        console.log('- 可以通过VCP系统调用这些功能');
    } else {
        console.log('⚠️  部分演示失败，请检查插件配置');
    }
    
    console.log('\n🔗 相关资源:');
    console.log('- 快速开始: 快速开始.md');
    console.log('- 完整文档: README.md');
    console.log('- API指南: 真实API使用指南.md');
    console.log('- 即梦AI官网: https://jimeng.jianying.com/');
}

// 检查配置
function checkSetup() {
    const fs = require('fs');
    const configPath = path.join(__dirname, 'config.env');
    
    if (!fs.existsSync(configPath)) {
        console.log('❌ 配置文件不存在！');
        console.log('请确保 config.env 文件存在');
        return false;
    }

    console.log('✅ 配置文件检查通过');
    return true;
}

// 主函数
async function main() {
    console.log('🔧 检查插件配置...');
    
    if (!checkSetup()) {
        process.exit(1);
    }

    await runAllDemos();
}

// 运行演示
if (require.main === module) {
    main().catch((error) => {
        console.error('❌ 演示运行失败:', error.message);
        process.exit(1);
    });
}
