// 智能浏览器助手测试脚本
const { spawn } = require('child_process');
const path = require('path');

async function testSmartBrowserAssistant() {
    console.log('🌐 测试智能浏览器助手插件...\n');
    
    const tests = [
        {
            name: '页面分析测试',
            params: {
                action: 'analyze_page',
                analysis_type: 'content',
                extract_images: false,
                extract_links: true,
                summarize: true,
                config: {
                    DEBUG_MODE: 'true',
                    CHROME_EXTENSION_PORT: '8080'
                }
            }
        },
        {
            name: '表单填写测试',
            params: {
                action: 'fill_form',
                form_data: JSON.stringify({
                    name: '张三',
                    email: '<EMAIL>',
                    phone: '13800138000',
                    message: '这是一条测试消息'
                }),
                submit_form: false,
                validate_before_submit: true,
                config: {
                    DEBUG_MODE: 'true',
                    ENABLE_SMART_FORMS: 'true'
                }
            }
        },
        {
            name: '智能购物测试',
            params: {
                action: 'smart_shopping',
                shopping_action: 'search',
                product_name: 'iPhone 15 Pro',
                max_price: '8000',
                min_rating: '4.0',
                config: {
                    DEBUG_MODE: 'true',
                    ENABLE_PRICE_TRACKING: 'true'
                }
            }
        },
        {
            name: '内容提取测试',
            params: {
                action: 'extract_content',
                content_type: 'text',
                format_output: 'markdown',
                max_items: '10',
                config: {
                    DEBUG_MODE: 'true',
                    ENABLE_CONTENT_EXTRACTION: 'true'
                }
            }
        },
        {
            name: '工作流测试',
            params: {
                action: 'run_workflow',
                workflow_steps: JSON.stringify([
                    {
                        action: 'navigate',
                        url: 'https://www.google.com'
                    },
                    {
                        action: 'type',
                        selector: 'input[name="q"]',
                        text: 'VCP智能助手'
                    },
                    {
                        action: 'wait',
                        duration: 1000
                    },
                    {
                        action: 'click',
                        selector: 'input[type="submit"]'
                    }
                ]),
                error_handling: 'skip',
                max_retries: '2',
                step_delay: '1500',
                config: {
                    DEBUG_MODE: 'true'
                }
            }
        }
    ];
    
    for (const test of tests) {
        console.log(`\n🧪 ${test.name}:`);
        console.log('=' .repeat(60));
        
        try {
            // 运行插件
            const pluginPath = path.join(__dirname, 'SmartBrowserAssistant.js');
            const child = spawn('node', [pluginPath], {
                stdio: ['pipe', 'pipe', 'pipe'],
                cwd: __dirname
            });
            
            child.stdin.write(JSON.stringify(test.params));
            child.stdin.end();
            
            let stdout = '';
            let stderr = '';
            
            child.stdout.on('data', (data) => {
                stdout += data.toString();
            });
            
            child.stderr.on('data', (data) => {
                stderr += data.toString();
            });
            
            await new Promise((resolve) => {
                child.on('close', (code) => {
                    console.log(`📊 退出码: ${code}`);
                    
                    if (stderr) {
                        console.log('🐛 调试信息:');
                        console.log(stderr);
                    }
                    
                    if (stdout) {
                        try {
                            const result = JSON.parse(stdout);
                            
                            if (result.status === 'success') {
                                console.log('✅ 测试成功！');
                                console.log('\n📋 结果:');
                                console.log(result.result);
                            } else {
                                console.log('❌ 测试失败:', result.error);
                                if (result.messageForAI) {
                                    console.log('💬 AI消息:', result.messageForAI);
                                }
                            }
                        } catch (error) {
                            console.log('❌ 解析输出失败:', error.message);
                            console.log('原始输出:', stdout);
                        }
                    } else {
                        console.log('❌ 没有收到输出');
                    }
                    
                    resolve();
                });
                
                setTimeout(() => {
                    if (!child.killed) {
                        console.log('⏰ 测试超时');
                        child.kill();
                        resolve();
                    }
                }, 30000);
            });
            
        } catch (error) {
            console.error('❌ 测试异常:', error.message);
        }
    }
}

// 运行测试
testSmartBrowserAssistant().then(() => {
    console.log('\n🎉 智能浏览器助手插件测试完成！');
    console.log('\n💡 这个插件展示了VCP的革命性浏览器控制能力:');
    console.log('- 🔍 智能页面分析和理解');
    console.log('- 📝 自动化表单填写和提交');
    console.log('- 🛒 智能购物和价格比较');
    console.log('- 📊 高级数据提取和结构化');
    console.log('- 🔄 复杂工作流自动化执行');
    console.log('- 🌐 实时页面监控和响应');
    console.log('\n🚀 注意: 完整功能需要安装Chrome扩展并建立WebSocket连接');
    console.log('📖 详细使用说明请查看 README.md 文件');
});
