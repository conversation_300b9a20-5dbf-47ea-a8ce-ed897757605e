# 即梦AI插件使用说明

## 🎯 插件概述

即梦AI插件是为VCP系统开发的异步插件，用于调用即梦AI（剪映AI工具）进行AI内容创作。支持图片生成、视频生成、数字人创作等多种功能。

## ✨ 主要功能

### 🎨 图片生成
- **支持2K高清生成**：提供standard、hd、2k三种质量选项
- **多种风格选择**：realistic（写实）、anime（动漫）、artistic（艺术）等
- **自定义宽高比**：支持1:1、16:9、9:16等多种比例
- **所梦即所得**：精准响应用户的创意需求

### 🎬 视频生成
- **多镜头及运镜**：支持zoom_in、zoom_out、pan_left、pan_right、static等运镜方式
- **自定义时长**：支持5-60秒的视频时长设置
- **高清输出**：提供standard和hd两种质量选项
- **精准响应**：智能理解用户的视频创意需求

### 👤 数字人生成
- **大师模式**：采用先进的AI技术创建栩栩如生的虚拟人物
- **角色定制**：支持详细的角色外观描述
- **动作控制**：可指定具体的动作和表情
- **场景设置**：支持自定义背景环境

## 🚀 快速开始

### 1. 安装配置

确保插件文件已正确放置在VCP的Plugin目录下：
```
Plugin/JimengAI/
├── plugin-manifest.json
├── JimengAI.js
├── config.env
├── README.md
└── test.js
```

### 2. 配置Session ID

编辑 `config.env` 文件，设置你的即梦AI会话ID：
```env
SESSION_ID=你的会话ID
BASE_URL=https://jimeng.jianying.com
DEBUG_MODE=false
```

### 3. 测试插件

运行测试脚本验证插件功能：
```bash
cd Plugin/JimengAI
node test.js
```

## 📝 使用方法

### 图片生成示例

```
<<<[TOOL_REQUEST]>>>
maid:「始」AI助手「末」,
tool_name:「始」JimengAI「末」,
type:「始」image「末」,
prompt:「始」一只可爱的小猫在花园里玩耍，阳光明媚，高清摄影风格「末」,
quality:「始」2k「末」,
style:「始」realistic「末」,
aspect_ratio:「始」16:9「末」
<<<[END_TOOL_REQUEST]>>>
```

### 视频生成示例

```
<<<[TOOL_REQUEST]>>>
maid:「始」AI助手「末」,
tool_name:「始」JimengAI「末」,
type:「始」video「末」,
prompt:「始」海边日落，海浪轻拍沙滩，镜头缓慢推进「末」,
duration:「始」10「末」,
motion:「始」zoom_in「末」,
quality:「始」hd「末」
<<<[END_TOOL_REQUEST]>>>
```

### 数字人生成示例

```
<<<[TOOL_REQUEST]>>>
maid:「始」AI助手「末」,
tool_name:「始」JimengAI「末」,
type:「始」digital_human「末」,
character:「始」年轻女性，长发，职业装，亲和力强「末」,
action:「始」挥手打招呼「末」,
expression:「始」微笑「末」,
background:「始」现代办公室「末」
<<<[END_TOOL_REQUEST]>>>
```

## ⚙️ 参数说明

### 图片生成参数
- **prompt**（必需）：图片描述提示词
- **quality**（可选）：图片质量，可选值：standard、hd、2k，默认hd
- **style**（可选）：图片风格，如：realistic、anime、artistic
- **aspect_ratio**（可选）：宽高比，如：1:1、16:9、9:16，默认1:1

### 视频生成参数
- **prompt**（必需）：视频描述提示词
- **duration**（可选）：视频时长，单位秒，默认5秒
- **motion**（可选）：运镜方式，如：zoom_in、zoom_out、pan_left、pan_right、static
- **quality**（可选）：视频质量，可选值：standard、hd，默认hd

### 数字人生成参数
- **character**（必需）：数字人角色描述
- **action**（可选）：动作描述
- **expression**（可选）：表情描述
- **background**（可选）：背景描述

## 🔧 配置选项

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| SESSION_ID | string | - | 即梦AI会话ID（必需） |
| BASE_URL | string | https://jimeng.jianying.com | API基础URL |
| DEBUG_MODE | boolean | false | 调试模式 |
| REQUEST_TIMEOUT | number | 120000 | 请求超时时间（毫秒） |
| MAX_RETRIES | number | 3 | 最大重试次数 |
| REQUEST_INTERVAL | number | 1000 | 请求间隔（毫秒） |

## ⚠️ 注意事项

1. **会话有效性**：Session ID有时效性，如果出现认证错误，请重新获取
2. **请求频率**：避免过于频繁的请求，建议设置适当的请求间隔
3. **异步处理**：视频和数字人生成是异步任务，需要等待一定时间
4. **网络环境**：确保网络连接稳定，某些功能可能需要特定的网络环境
5. **API限制**：请遵守即梦AI的使用条款和API调用限制

## 🐛 故障排除

### 常见问题

1. **认证失败**
   - 检查SESSION_ID是否正确且有效
   - 确认网络连接正常
   - 尝试重新获取Session ID

2. **请求超时**
   - 检查网络连接
   - 增加REQUEST_TIMEOUT值
   - 确认即梦AI服务状态

3. **参数错误**
   - 检查参数格式是否正确
   - 确认必需参数已提供
   - 查看调试日志（开启DEBUG_MODE）

### 调试方法

1. 开启调试模式：
   ```env
   DEBUG_MODE=true
   ```

2. 查看日志输出，了解详细的执行过程

3. 运行测试脚本验证功能：
   ```bash
   node test.js
   ```

## 📞 技术支持

如果遇到问题，请：
1. 查看本文档的故障排除部分
2. 检查VCP系统日志
3. 确认即梦AI官方服务状态
4. 联系VCP技术支持团队

## 🔄 版本更新

### v1.0.0
- 初始版本发布
- 支持图片生成功能
- 支持视频生成功能
- 支持数字人生成功能
- 完整的错误处理和日志记录
- 模拟模式支持（用于开发测试）

---

**即梦AI插件** - 让AI创作触手可及，释放无限创意可能！
