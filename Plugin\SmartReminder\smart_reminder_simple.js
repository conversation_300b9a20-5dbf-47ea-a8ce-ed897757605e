#!/usr/bin/env node
/**
 * VCP SmartReminder Plugin - 简化版本
 * 快速响应，基本功能
 */

const fs = require('fs').promises;
const path = require('path');

class SimpleSmartReminder {
    constructor() {
        this.remindersFile = path.join(__dirname, 'reminders.json');
        this.config = this.loadConfig();
    }

    loadConfig() {
        return {
            enableEmail: process.env.enable_email === 'true',
            emailConfig: {
                host: process.env.email_smtp_host || 'smtp.gmail.com',
                port: parseInt(process.env.email_smtp_port) || 587,
                user: process.env.email_user,
                password: process.env.email_password,
                to: process.env.email_to || process.env.email_user
            }
        };
    }

    async loadReminders() {
        try {
            const data = await fs.readFile(this.remindersFile, 'utf8');
            return JSON.parse(data);
        } catch (error) {
            if (error.code === 'ENOENT') {
                return [];
            }
            throw error;
        }
    }

    async saveReminders(reminders) {
        await fs.writeFile(this.remindersFile, JSON.stringify(reminders, null, 2));
    }

    parseSimpleTime(timeExpression) {
        const now = new Date();
        const text = timeExpression.toLowerCase().trim();

        // 简单的时间解析
        if (text.includes('分钟后')) {
            const match = text.match(/(\d+)分钟后/);
            if (match) {
                const minutes = parseInt(match[1]);
                return new Date(now.getTime() + minutes * 60 * 1000);
            }
        }

        if (text.includes('小时后')) {
            const match = text.match(/(\d+)小时后/);
            if (match) {
                const hours = parseInt(match[1]);
                return new Date(now.getTime() + hours * 60 * 60 * 1000);
            }
        }

        if (text.includes('明天')) {
            const tomorrow = new Date(now);
            tomorrow.setDate(tomorrow.getDate() + 1);
            tomorrow.setHours(9, 0, 0, 0); // 默认上午9点
            return tomorrow;
        }

        // 默认1小时后
        return new Date(now.getTime() + 60 * 60 * 1000);
    }

    generateReminderId() {
        return `reminder_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    }

    async createReminder(reminderText, timeExpression, notificationMethods = 'websocket') {
        const reminders = await this.loadReminders();

        const triggerTime = this.parseSimpleTime(timeExpression);

        if (triggerTime.getTime() <= Date.now()) {
            throw new Error('提醒时间不能是过去的时间');
        }

        const reminder = {
            id: this.generateReminderId(),
            text: reminderText,
            originalTimeExpression: timeExpression,
            triggerTime: triggerTime.toISOString(),
            notificationMethods: notificationMethods.split(',').map(m => m.trim()),
            createdAt: new Date().toISOString(),
            isActive: true
        };

        reminders.push(reminder);
        await this.saveReminders(reminders);

        // 如果选择了邮件通知，立即发送确认邮件
        if (reminder.notificationMethods.includes('email') && this.config.enableEmail) {
            try {
                await this.sendConfirmationEmail(reminder);
            } catch (error) {
                console.error('发送确认邮件失败:', error.message);
            }
        }

        return {
            success: true,
            reminder: reminder,
            message: `提醒已设置：${reminderText}，将在 ${triggerTime.toLocaleString('zh-CN')} 提醒您`
        };
    }

    async sendConfirmationEmail(reminder) {
        if (!this.config.emailConfig.user || !this.config.emailConfig.password) {
            throw new Error('邮件配置不完整');
        }

        try {
            const nodemailer = require('nodemailer');

            const transporter = nodemailer.createTransport({
                host: this.config.emailConfig.host,
                port: this.config.emailConfig.port,
                secure: this.config.emailConfig.port === 465,
                auth: {
                    user: this.config.emailConfig.user,
                    pass: this.config.emailConfig.password
                }
            });

            const triggerTime = new Date(reminder.triggerTime);

            const mailOptions = {
                from: this.config.emailConfig.user,
                to: this.config.emailConfig.to,
                subject: '📅 智能提醒已设置',
                html: `
                    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                        <h2 style="color: #2196F3;">🔔 提醒设置成功</h2>
                        <div style="background: #f5f5f5; padding: 20px; border-radius: 8px; margin: 20px 0;">
                            <h3 style="margin-top: 0; color: #333;">提醒内容</h3>
                            <p style="font-size: 16px; color: #666;">${reminder.text}</p>
                        </div>
                        <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin: 20px 0;">
                            <h3 style="margin-top: 0; color: #1976D2;">⏰ 提醒时间</h3>
                            <p style="font-size: 16px; color: #1976D2; font-weight: bold;">
                                ${triggerTime.toLocaleString('zh-CN', {
                                    year: 'numeric',
                                    month: 'long',
                                    day: 'numeric',
                                    hour: '2-digit',
                                    minute: '2-digit',
                                    weekday: 'long'
                                })}
                            </p>
                        </div>
                        <div style="background: #fff3e0; padding: 15px; border-radius: 8px; margin: 20px 0;">
                            <h3 style="margin-top: 0; color: #F57C00;">📋 提醒详情</h3>
                            <ul style="color: #666;">
                                <li>提醒ID: ${reminder.id}</li>
                                <li>创建时间: ${new Date(reminder.createdAt).toLocaleString('zh-CN')}</li>
                                <li>通知方式: ${reminder.notificationMethods.join(', ')}</li>
                            </ul>
                        </div>
                        <p style="color: #999; font-size: 14px; text-align: center; margin-top: 30px;">
                            此邮件由 VCP SmartReminder 智能提醒系统自动发送
                        </p>
                    </div>
                `
            };

            await transporter.sendMail(mailOptions);
            return { success: true, message: '确认邮件已发送' };

        } catch (error) {
            throw new Error(`邮件发送失败: ${error.message}`);
        }
    }

    async sendReminderEmail(reminder) {
        if (!this.config.emailConfig.user || !this.config.emailConfig.password) {
            return;
        }

        try {
            const nodemailer = require('nodemailer');

            const transporter = nodemailer.createTransport({
                host: this.config.emailConfig.host,
                port: this.config.emailConfig.port,
                secure: this.config.emailConfig.port === 465,
                auth: {
                    user: this.config.emailConfig.user,
                    pass: this.config.emailConfig.password
                }
            });

            const mailOptions = {
                from: this.config.emailConfig.user,
                to: this.config.emailConfig.to,
                subject: '🔔 提醒通知',
                html: `
                    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                        <h2 style="color: #FF5722;">🚨 提醒时间到了！</h2>
                        <div style="background: #ffebee; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #FF5722;">
                            <h3 style="margin-top: 0; color: #FF5722;">提醒内容</h3>
                            <p style="font-size: 18px; color: #333; font-weight: bold;">${reminder.text}</p>
                        </div>
                        <div style="background: #f5f5f5; padding: 15px; border-radius: 8px; margin: 20px 0;">
                            <p style="color: #666; margin: 0;">
                                <strong>原定时间:</strong> ${new Date(reminder.triggerTime).toLocaleString('zh-CN')}<br>
                                <strong>提醒ID:</strong> ${reminder.id}
                            </p>
                        </div>
                        <p style="color: #999; font-size: 14px; text-align: center; margin-top: 30px;">
                            此邮件由 VCP SmartReminder 智能提醒系统自动发送
                        </p>
                    </div>
                `
            };

            await transporter.sendMail(mailOptions);

        } catch (error) {
            console.error('发送提醒邮件失败:', error.message);
        }
    }

    async listReminders() {
        const reminders = await this.loadReminders();
        const activeReminders = reminders.filter(r => r.isActive);
        
        return activeReminders.map(reminder => ({
            id: reminder.id,
            text: reminder.text,
            triggerTime: new Date(reminder.triggerTime).toLocaleString('zh-CN'),
            isActive: reminder.isActive
        }));
    }

    async deleteReminder(reminderId) {
        const reminders = await this.loadReminders();
        const index = reminders.findIndex(r => r.id === reminderId);
        
        if (index === -1) {
            throw new Error('提醒不存在');
        }

        reminders.splice(index, 1);
        await this.saveReminders(reminders);

        return { success: true, message: '提醒已删除' };
    }

    async searchReminders(keyword) {
        const reminders = await this.loadReminders();
        const results = reminders.filter(r => 
            r.isActive && r.text.toLowerCase().includes(keyword.toLowerCase())
        );

        return results.map(reminder => ({
            id: reminder.id,
            text: reminder.text,
            triggerTime: new Date(reminder.triggerTime).toLocaleString('zh-CN')
        }));
    }
}

// 主函数
async function main() {
    const startTime = Date.now();
    
    try {
        // 设置严格的超时保护
        const timeout = setTimeout(() => {
            console.log(JSON.stringify({
                status: 'error',
                error: 'Plugin execution timeout',
                messageForAI: '插件执行超时，请稍后重试'
            }));
            process.exit(1);
        }, 25000); // 25秒超时

        let inputData = '';
        
        // 使用 Promise 包装输入读取
        const inputPromise = new Promise((resolve, reject) => {
            process.stdin.on('data', (chunk) => {
                inputData += chunk;
            });

            process.stdin.on('end', () => {
                resolve(inputData.trim());
            });

            process.stdin.on('error', reject);
        });

        const input = await inputPromise;
        clearTimeout(timeout);

        if (!input) {
            throw new Error('No input data received');
        }

        const params = JSON.parse(input);
        const reminder = new SimpleSmartReminder();
        
        let result;
        
        switch (params.command) {
            case 'create':
                if (!params.reminder_text || !params.time_expression) {
                    throw new Error('缺少必需参数：reminder_text 和 time_expression');
                }
                result = await reminder.createReminder(
                    params.reminder_text,
                    params.time_expression,
                    params.notification_methods || 'websocket'
                );
                break;
                
            case 'list':
                const reminders = await reminder.listReminders();
                result = {
                    success: true,
                    reminders: reminders,
                    count: reminders.length,
                    message: `找到 ${reminders.length} 个活跃提醒`
                };
                break;
                
            case 'delete':
                if (!params.reminder_id) {
                    throw new Error('缺少必需参数：reminder_id');
                }
                result = await reminder.deleteReminder(params.reminder_id);
                break;
                
            case 'search':
                if (!params.keyword) {
                    throw new Error('缺少必需参数：keyword');
                }
                const searchResults = await reminder.searchReminders(params.keyword);
                result = {
                    success: true,
                    reminders: searchResults,
                    count: searchResults.length,
                    message: `找到 ${searchResults.length} 个匹配的提醒`
                };
                break;
                
            default:
                throw new Error(`未知命令：${params.command}`);
        }

        const response = {
            status: 'success',
            result: result,
            messageForAI: result.message || '操作完成',
            executionTime: Date.now() - startTime
        };

        console.log(JSON.stringify(response));
        
    } catch (error) {
        const errorResponse = {
            status: 'error',
            error: error.message,
            messageForAI: `智能提醒操作失败：${error.message}`,
            executionTime: Date.now() - startTime
        };
        console.log(JSON.stringify(errorResponse));
    }
}

if (require.main === module) {
    main();
}

module.exports = SimpleSmartReminder;
