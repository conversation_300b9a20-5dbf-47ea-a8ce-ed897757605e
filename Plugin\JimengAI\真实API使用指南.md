# 即梦AI插件真实API使用指南

## 🎯 概述

本插件提供了两种工作模式：
1. **模拟模式**：用于测试和演示，返回模拟结果
2. **真实API模式**：调用真实的即梦AI服务（需要额外配置）

## 🔧 配置说明

### 基础配置

在 `config.env` 文件中设置：

```env
# 即梦AI会话ID（必需）
SESSION_ID=你的会话ID

# 工作模式选择
USE_REAL_API=false  # false=模拟模式, true=真实API模式

# 调试模式
DEBUG_MODE=true
```

### 获取Session ID

1. 访问 [即梦AI官网](https://jimeng.jianying.com/)
2. 登录你的账号
3. 按F12打开开发者工具
4. 进入 Application → Cookies
5. 找到 `sessionid` 的值
6. 将该值填入 `config.env` 的 `SESSION_ID`

## 🚀 使用真实API的方法

### 方法一：使用 jimeng-free-api 项目（推荐）

这是最简单可靠的方法：

#### 1. 部署 jimeng-free-api

```bash
# 克隆项目
git clone https://github.com/LLM-Red-Team/jimeng-free-api
cd jimeng-free-api

# 安装依赖
npm install

# 启动服务
npm start
```

或使用Docker：

```bash
docker run -it -d --init --name jimeng-free-api -p 8000:8000 -e TZ=Asia/Shanghai vinlic/jimeng-free-api:latest
```

#### 2. 修改插件配置

更新 `config.env`：

```env
# 使用 jimeng-free-api 作为代理
BASE_URL=http://localhost:8000
USE_REAL_API=true
```

#### 3. 修改插件代码

在 `JimengAI.js` 中，将API端点修改为：

```javascript
// 图片生成
path: '/v1/images/generations'

// 对话补全（用于视频生成）
path: '/v1/chat/completions'
```

请求头使用：

```javascript
headers: {
    'Authorization': `Bearer ${config.SESSION_ID}`,
    'Content-Type': 'application/json'
}
```

### 方法二：直接调用即梦AI内部API

这需要逆向工程即梦AI的网页版API：

#### 1. 抓包分析

1. 打开即梦AI网站
2. 使用F12开发者工具
3. 进入Network标签
4. 执行图片生成操作
5. 分析请求的URL、请求头、请求体格式

#### 2. 更新插件代码

根据抓包结果修改：

```javascript
// 示例：可能的API端点
const apiEndpoints = {
    image: '/api/v1/generate/image',
    video: '/api/v1/generate/video',
    chat: '/api/v1/chat'
};

// 示例：可能的请求格式
const requestData = {
    prompt: prompt,
    model: 'jimeng-3.0',
    width: 1024,
    height: 1024,
    // 其他参数...
};
```

## 📝 API调用示例

### 使用 jimeng-free-api 的调用示例

```javascript
// 图片生成
const response = await fetch('http://localhost:8000/v1/images/generations', {
    method: 'POST',
    headers: {
        'Authorization': `Bearer ${sessionId}`,
        'Content-Type': 'application/json'
    },
    body: JSON.stringify({
        model: 'jimeng-3.0',
        prompt: '一只可爱的小猫',
        width: 1024,
        height: 1024
    })
});

// 对话补全
const response = await fetch('http://localhost:8000/v1/chat/completions', {
    method: 'POST',
    headers: {
        'Authorization': `Bearer ${sessionId}`,
        'Content-Type': 'application/json'
    },
    body: JSON.stringify({
        model: 'jimeng-3.0',
        messages: [
            {
                role: 'user',
                content: '生成一个视频：海边日落'
            }
        ]
    })
});
```

## ⚠️ 注意事项

### 1. Session ID 有效性

- Session ID有时效性，通常几小时到几天
- 如果出现认证错误，需要重新获取
- 建议定期更新Session ID

### 2. 请求频率限制

- 避免过于频繁的请求
- 建议设置请求间隔（1-2秒）
- 遵守即梦AI的使用条款

### 3. 网络环境

- 确保网络连接稳定
- 某些地区可能需要特殊网络环境
- 建议使用国内服务器部署

### 4. 法律合规

- 仅供学习和研究使用
- 禁止商业用途
- 遵守相关法律法规

## 🔍 故障排除

### 常见错误及解决方案

#### 1. 302重定向错误

```
API请求失败: 302 - <html><head><title>302 Found</title></head>...
```

**原因**：API端点不正确或需要特殊认证

**解决方案**：
- 使用 jimeng-free-api 项目
- 重新抓包分析正确的API端点

#### 2. 认证失败

```
API请求失败: 401 - Unauthorized
```

**原因**：Session ID无效或过期

**解决方案**：
- 重新获取Session ID
- 检查Cookie格式是否正确

#### 3. 请求超时

```
请求超时
```

**原因**：网络连接问题或服务器响应慢

**解决方案**：
- 检查网络连接
- 增加超时时间
- 使用代理服务器

## 📚 参考资源

- [即梦AI官网](https://jimeng.jianying.com/)
- [jimeng-free-api项目](https://github.com/LLM-Red-Team/jimeng-free-api)
- [火山引擎API文档](https://www.volcengine.com/docs/85621/1537648)

## 🤝 贡献

如果你成功实现了真实API调用，欢迎：
1. 提交代码改进
2. 分享配置经验
3. 报告问题和建议

---

**免责声明**：本插件仅供学习研究使用，请遵守即梦AI的服务条款和相关法律法规。
