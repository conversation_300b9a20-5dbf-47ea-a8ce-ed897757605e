{"manifestVersion": "1.0.0", "name": "SmartReminder", "version": "1.0.0", "displayName": "智能提醒助手", "description": "AI驱动的智能提醒系统，支持自然语言解析和多种提醒方式", "author": "VCP Assistant", "pluginType": "synchronous", "entryPoint": {"type": "node", "command": "node smart_reminder_simple.js"}, "communication": {"protocol": "stdio", "timeout": 60000}, "configSchema": {"timezone": "string", "default_reminder_advance": "integer", "max_reminders": "integer", "enable_email": "boolean", "email_smtp_host": "string", "email_smtp_port": "integer", "email_user": "string", "email_password": "string", "notification_sound": "boolean"}, "capabilities": {"systemPromptPlaceholders": [], "invocationCommands": [{"commandIdentifier": "SmartReminderRequest", "description": "智能提醒助手，支持自然语言设置提醒和管理提醒事项：\n\n**支持的命令：**\n- `create`: 创建新提醒（支持自然语言时间解析）\n- `list`: 查看所有提醒\n- `delete`: 删除指定提醒\n- `update`: 更新提醒内容或时间\n- `search`: 搜索提醒内容\n\n**自然语言时间解析示例：**\n- \"明天上午9点\"\n- \"下周一下午2点\"\n- \"3天后\"\n- \"每天早上8点\"（重复提醒）\n- \"工作日晚上6点\"（工作日重复）\n\n**调用格式：**\n```\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」SmartReminder「末」,\ncommand:「始」命令类型「末」,\nreminder_text:「始」提醒内容「末」,\ntime_expression:「始」时间表达式（自然语言）「末」,\nreminder_id:「始」提醒ID（删除/更新时使用）「末」,\nrepeat_type:「始」重复类型：once/daily/weekly/monthly「末」,\nnotification_methods:「始」通知方式：websocket/email/both「末」\n<<<[END_TOOL_REQUEST]>>>\n```", "example": "```\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」SmartReminder「末」,\ncommand:「始」create「末」,\nreminder_text:「始」参加项目会议，记得准备PPT「末」,\ntime_expression:「始」明天下午3点「末」,\nrepeat_type:「始」once「末」,\nnotification_methods:「始」websocket「末」\n<<<[END_TOOL_REQUEST]>>>\n```"}], "responseFormatToAI": "### 智能提醒助手\n{result}"}, "webSocketPush": {"enabled": true, "usePluginResultAsMessage": false, "messageType": "smart_reminder_notification", "targetClientType": "SmartReminder"}, "dependencies": {"node": ">=14.0.0", "libraries": ["moment", "node-schedule", "nodemailer", "chrono-node"]}}