const fs = require('fs');
const path = require('path');
const WebSocket = require('ws');

class SmartBrowserAssistant {
    constructor() {
        this.config = {};
        this.debugMode = false;
        this.wsConnection = null;
        this.monitoringTasks = new Map();
        this.workflowHistory = [];
    }

    log(message) {
        if (this.debugMode) {
            console.error(`[SmartBrowserAssistant] ${message}`);
        }
    }

    loadConfig(configData) {
        try {
            let config = {};
            if (configData && configData.trim()) {
                try {
                    config = JSON.parse(configData);
                } catch (parseError) {
                    this.log(`Failed to parse config: ${parseError.message}`);
                }
            }

            this.config = config;
            this.debugMode = this.config.DEBUG_MODE === 'true' || this.config.DEBUG_MODE === true;
            
            // 设置默认值
            this.config.CHROME_EXTENSION_PORT = parseInt(this.config.CHROME_EXTENSION_PORT) || 8080;
            this.config.AUTO_SCROLL_DELAY = parseInt(this.config.AUTO_SCROLL_DELAY) || 1000;
            this.config.MAX_WAIT_TIME = parseInt(this.config.MAX_WAIT_TIME) || 30000;
            this.config.SCREENSHOT_QUALITY = this.config.SCREENSHOT_QUALITY || 'medium';
            this.config.ENABLE_SMART_FORMS = this.config.ENABLE_SMART_FORMS !== 'false';
            this.config.ENABLE_PRICE_TRACKING = this.config.ENABLE_PRICE_TRACKING !== 'false';
            this.config.ENABLE_CONTENT_EXTRACTION = this.config.ENABLE_CONTENT_EXTRACTION !== 'false';
            
            this.log('Configuration loaded successfully');
        } catch (error) {
            throw new Error(`Failed to load configuration: ${error.message}`);
        }
    }

    async connectToChrome() {
        return new Promise((resolve, reject) => {
            try {
                this.wsConnection = new WebSocket(`ws://localhost:${this.config.CHROME_EXTENSION_PORT}`);
                
                this.wsConnection.on('open', () => {
                    this.log('Connected to Chrome extension');
                    resolve();
                });
                
                this.wsConnection.on('error', (error) => {
                    this.log(`WebSocket error: ${error.message}`);
                    reject(error);
                });
                
                this.wsConnection.on('message', (data) => {
                    this.handleChromeMessage(JSON.parse(data));
                });
                
            } catch (error) {
                reject(error);
            }
        });
    }

    async sendChromeCommand(command) {
        return new Promise((resolve, reject) => {
            if (!this.wsConnection || this.wsConnection.readyState !== WebSocket.OPEN) {
                reject(new Error('Chrome connection not available'));
                return;
            }

            const commandId = Date.now().toString();
            command.id = commandId;

            const timeout = setTimeout(() => {
                reject(new Error('Command timeout'));
            }, this.config.MAX_WAIT_TIME);

            const messageHandler = (data) => {
                const response = JSON.parse(data);
                if (response.id === commandId) {
                    clearTimeout(timeout);
                    this.wsConnection.removeListener('message', messageHandler);
                    if (response.success) {
                        resolve(response.data);
                    } else {
                        reject(new Error(response.error));
                    }
                }
            };

            this.wsConnection.on('message', messageHandler);
            this.wsConnection.send(JSON.stringify(command));
        });
    }

    handleChromeMessage(message) {
        this.log(`Received Chrome message: ${JSON.stringify(message)}`);
        
        // 处理监控任务的回调
        if (message.type === 'monitor_update') {
            this.handleMonitorUpdate(message);
        }
    }

    async analyzePage(options = {}) {
        const {
            analysis_type = 'content',
            extract_images = false,
            extract_links = true,
            summarize = true
        } = options;

        try {
            // 获取页面基本信息
            const pageInfo = await this.sendChromeCommand({
                action: 'get_page_info',
                include_images: extract_images,
                include_links: extract_links
            });

            let analysis = {
                url: pageInfo.url,
                title: pageInfo.title,
                timestamp: new Date().toISOString()
            };

            if (analysis_type === 'content' || analysis_type === 'all') {
                analysis.content = {
                    text_length: pageInfo.text?.length || 0,
                    paragraph_count: pageInfo.paragraphs?.length || 0,
                    heading_count: pageInfo.headings?.length || 0,
                    main_content: pageInfo.main_content || ''
                };
            }

            if (analysis_type === 'structure' || analysis_type === 'all') {
                analysis.structure = {
                    forms: pageInfo.forms || [],
                    buttons: pageInfo.buttons || [],
                    inputs: pageInfo.inputs || [],
                    navigation: pageInfo.navigation || []
                };
            }

            if (analysis_type === 'seo' || analysis_type === 'all') {
                analysis.seo = {
                    meta_description: pageInfo.meta_description || '',
                    meta_keywords: pageInfo.meta_keywords || '',
                    h1_tags: pageInfo.h1_tags || [],
                    alt_texts: pageInfo.alt_texts || []
                };
            }

            if (extract_links) {
                analysis.links = pageInfo.links || [];
            }

            if (extract_images) {
                analysis.images = pageInfo.images || [];
            }

            if (summarize && pageInfo.main_content) {
                analysis.summary = this.generateContentSummary(pageInfo.main_content);
            }

            return this.formatAnalysisReport(analysis);

        } catch (error) {
            throw new Error(`Page analysis failed: ${error.message}`);
        }
    }

    async fillForm(formData, options = {}) {
        const {
            submit_form = false,
            validate_before_submit = true,
            form_selector = null
        } = options;

        try {
            const result = await this.sendChromeCommand({
                action: 'fill_form',
                form_data: formData,
                form_selector: form_selector,
                validate: validate_before_submit
            });

            let response = `📝 表单填写完成\n\n`;
            response += `✅ 成功填写字段: ${result.filled_fields?.length || 0}\n`;
            
            if (result.failed_fields?.length > 0) {
                response += `❌ 填写失败字段: ${result.failed_fields.join(', ')}\n`;
            }

            if (submit_form && result.filled_fields?.length > 0) {
                const submitResult = await this.sendChromeCommand({
                    action: 'submit_form',
                    form_selector: form_selector
                });
                
                if (submitResult.success) {
                    response += `\n🚀 表单提交成功`;
                } else {
                    response += `\n❌ 表单提交失败: ${submitResult.error}`;
                }
            }

            return response;

        } catch (error) {
            throw new Error(`Form filling failed: ${error.message}`);
        }
    }

    async smartShopping(options = {}) {
        const {
            shopping_action,
            product_name,
            max_price,
            min_rating,
            store_preference
        } = options;

        try {
            let response = `🛒 智能购物助手\n\n`;

            switch (shopping_action) {
                case 'search':
                    const searchResults = await this.searchProducts(product_name, {
                        max_price,
                        min_rating,
                        store_preference
                    });
                    response += this.formatSearchResults(searchResults);
                    break;

                case 'compare_prices':
                    const priceComparison = await this.comparePrices(product_name);
                    response += this.formatPriceComparison(priceComparison);
                    break;

                case 'add_to_cart':
                    const cartResult = await this.addToCart();
                    response += cartResult.success ? '✅ 商品已添加到购物车' : `❌ 添加失败: ${cartResult.error}`;
                    break;

                case 'track_price':
                    const trackingResult = await this.setupPriceTracking(product_name, max_price);
                    response += trackingResult;
                    break;

                default:
                    throw new Error(`Unknown shopping action: ${shopping_action}`);
            }

            return response;

        } catch (error) {
            throw new Error(`Smart shopping failed: ${error.message}`);
        }
    }

    async extractContent(options = {}) {
        const {
            content_type,
            filter_criteria,
            format_output = 'markdown',
            max_items = 50
        } = options;

        try {
            const extractedData = await this.sendChromeCommand({
                action: 'extract_content',
                content_type: content_type,
                filter: filter_criteria,
                max_items: max_items
            });

            return this.formatExtractedContent(extractedData, format_output);

        } catch (error) {
            throw new Error(`Content extraction failed: ${error.message}`);
        }
    }

    async runWorkflow(workflowSteps, options = {}) {
        const {
            error_handling = 'stop',
            max_retries = 3,
            step_delay = 1000
        } = options;

        let response = `🔄 自动化工作流执行\n\n`;
        let executedSteps = 0;
        let failedSteps = 0;

        try {
            for (let i = 0; i < workflowSteps.length; i++) {
                const step = workflowSteps[i];
                response += `步骤 ${i + 1}: ${step.action}\n`;

                let retries = 0;
                let stepSuccess = false;

                while (retries <= max_retries && !stepSuccess) {
                    try {
                        await this.executeWorkflowStep(step);
                        stepSuccess = true;
                        executedSteps++;
                        response += `✅ 执行成功\n`;
                    } catch (error) {
                        retries++;
                        if (retries <= max_retries) {
                            response += `⚠️ 重试 ${retries}/${max_retries}\n`;
                            await this.delay(step_delay);
                        } else {
                            failedSteps++;
                            response += `❌ 执行失败: ${error.message}\n`;
                            
                            if (error_handling === 'stop') {
                                break;
                            }
                        }
                    }
                }

                if (stepSuccess && i < workflowSteps.length - 1) {
                    await this.delay(step_delay);
                }
            }

            response += `\n📊 执行总结:\n`;
            response += `✅ 成功步骤: ${executedSteps}\n`;
            response += `❌ 失败步骤: ${failedSteps}\n`;

            return response;

        } catch (error) {
            throw new Error(`Workflow execution failed: ${error.message}`);
        }
    }

    async executeWorkflowStep(step) {
        switch (step.action) {
            case 'navigate':
                return await this.sendChromeCommand({
                    action: 'navigate',
                    url: step.url
                });
            
            case 'click':
                return await this.sendChromeCommand({
                    action: 'click',
                    selector: step.selector
                });
            
            case 'type':
                return await this.sendChromeCommand({
                    action: 'type',
                    selector: step.selector,
                    text: step.text
                });
            
            case 'wait':
                return await this.delay(step.duration || 1000);
            
            default:
                throw new Error(`Unknown workflow action: ${step.action}`);
        }
    }

    generateContentSummary(content) {
        // 简单的内容摘要生成
        const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 10);
        const summary = sentences.slice(0, 3).join('. ');
        return summary.length > 200 ? summary.substring(0, 200) + '...' : summary;
    }

    formatAnalysisReport(analysis) {
        let report = `📊 网页分析报告\n\n`;
        report += `🌐 URL: ${analysis.url}\n`;
        report += `📄 标题: ${analysis.title}\n`;
        report += `⏰ 分析时间: ${analysis.timestamp}\n\n`;

        if (analysis.content) {
            report += `📝 内容分析:\n`;
            report += `- 文本长度: ${analysis.content.text_length} 字符\n`;
            report += `- 段落数量: ${analysis.content.paragraph_count}\n`;
            report += `- 标题数量: ${analysis.content.heading_count}\n\n`;
        }

        if (analysis.structure) {
            report += `🏗️ 结构分析:\n`;
            report += `- 表单数量: ${analysis.structure.forms.length}\n`;
            report += `- 按钮数量: ${analysis.structure.buttons.length}\n`;
            report += `- 输入框数量: ${analysis.structure.inputs.length}\n\n`;
        }

        if (analysis.summary) {
            report += `📋 内容摘要:\n${analysis.summary}\n\n`;
        }

        return report;
    }

    formatSearchResults(results) {
        let output = `🔍 商品搜索结果:\n\n`;
        
        if (results && results.length > 0) {
            results.forEach((product, index) => {
                output += `${index + 1}. **${product.name}**\n`;
                output += `   💰 价格: ¥${product.price}\n`;
                output += `   ⭐ 评分: ${product.rating}/5\n`;
                output += `   🏪 商店: ${product.store}\n`;
                output += `   🔗 链接: ${product.url}\n\n`;
            });
        } else {
            output += `未找到相关商品\n`;
        }

        return output;
    }

    formatExtractedContent(data, format) {
        switch (format) {
            case 'json':
                return `\`\`\`json\n${JSON.stringify(data, null, 2)}\n\`\`\``;
            
            case 'csv':
                if (Array.isArray(data) && data.length > 0) {
                    const headers = Object.keys(data[0]);
                    let csv = headers.join(',') + '\n';
                    data.forEach(row => {
                        csv += headers.map(h => row[h] || '').join(',') + '\n';
                    });
                    return `\`\`\`csv\n${csv}\n\`\`\``;
                }
                return '无数据可导出为CSV格式';
            
            default: // markdown
                let output = `📋 提取的内容:\n\n`;
                if (Array.isArray(data)) {
                    data.forEach((item, index) => {
                        output += `${index + 1}. ${JSON.stringify(item)}\n`;
                    });
                } else {
                    output += JSON.stringify(data, null, 2);
                }
                return output;
        }
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    async searchProducts(productName, options = {}) {
        try {
            if (this.wsConnection && this.wsConnection.readyState === WebSocket.OPEN) {
                const searchResult = await this.sendChromeCommand({
                    action: 'search_products',
                    query: productName,
                    filters: options
                });
                return searchResult.products || [];
            } else {
                // 模拟数据作为后备
                return [
                    {
                        name: productName,
                        price: '999.00',
                        rating: '4.5',
                        store: '示例商店',
                        url: 'https://example.com/product'
                    }
                ];
            }
        } catch (error) {
            this.log(`Search products error: ${error.message}`);
            return [];
        }
    }

    async comparePrices(productName) {
        try {
            if (this.wsConnection && this.wsConnection.readyState === WebSocket.OPEN) {
                const comparison = await this.sendChromeCommand({
                    action: 'compare_prices',
                    product: productName
                });
                return comparison;
            } else {
                // 模拟价格比较数据
                return {
                    product: productName,
                    prices: [
                        { store: '商店A', price: '999.00', url: 'https://store-a.com' },
                        { store: '商店B', price: '1099.00', url: 'https://store-b.com' }
                    ],
                    lowest_price: '999.00',
                    highest_price: '1099.00'
                };
            }
        } catch (error) {
            this.log(`Compare prices error: ${error.message}`);
            return { product: productName, prices: [], error: error.message };
        }
    }

    async addToCart() {
        try {
            const result = await this.sendChromeCommand({
                action: 'click',
                selector: '.add-to-cart, [data-testid="add-to-cart"], .btn-add-cart, .addToCart'
            });
            return { success: true, message: '商品已添加到购物车' };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    async setupPriceTracking(productName, maxPrice) {
        const trackingId = Date.now().toString();
        this.monitoringTasks.set(trackingId, {
            type: 'price_tracking',
            product: productName,
            max_price: maxPrice,
            created_at: new Date().toISOString()
        });

        return `✅ 价格追踪已设置\n产品: ${productName}\n最高价格: ¥${maxPrice}\n追踪ID: ${trackingId}`;
    }

    formatPriceComparison(comparison) {
        let output = `💰 价格比较结果 - ${comparison.product}\n\n`;

        if (comparison.prices && comparison.prices.length > 0) {
            comparison.prices.forEach((item, index) => {
                output += `${index + 1}. **${item.store}**: ¥${item.price}\n`;
                output += `   🔗 ${item.url}\n\n`;
            });

            output += `📊 价格范围: ¥${comparison.lowest_price} - ¥${comparison.highest_price}\n`;
        } else {
            output += `未找到价格信息\n`;
        }

        return output;
    }

    async processRequest(params) {
        const { command } = params;
        
        // 尝试连接Chrome扩展
        try {
            await this.connectToChrome();
        } catch (error) {
            this.log(`Chrome connection failed: ${error.message}`);
            // 继续执行，某些功能可能不需要Chrome连接
        }

        switch (command) {
            case 'analyze_page':
                return await this.analyzePage(params);

            case 'fill_form':
                return await this.fillForm(JSON.parse(params.form_data || '{}'), params);

            case 'smart_shopping':
                return await this.smartShopping(params);

            case 'extract_content':
                return await this.extractContent(params);

            case 'run_workflow':
                return await this.runWorkflow(JSON.parse(params.workflow_steps || '[]'), params);

            default:
                throw new Error(`Unknown command: ${command}`);
        }
    }
}

// 主执行逻辑
async function main() {
    const assistant = new SmartBrowserAssistant();
    
    try {
        let inputData = '';
        process.stdin.setEncoding('utf8');
        
        for await (const chunk of process.stdin) {
            inputData += chunk;
        }
        
        if (!inputData.trim()) {
            throw new Error('No input data received');
        }

        const params = JSON.parse(inputData.trim());
        
        assistant.loadConfig(JSON.stringify(params.config || {}));
        
        const result = await assistant.processRequest(params);
        
        const response = {
            status: 'success',
            result: result
        };

        console.log(JSON.stringify(response));
        
    } catch (error) {
        const response = {
            status: 'error',
            error: error.message,
            messageForAI: `智能浏览器助手操作失败：${error.message}`
        };

        console.log(JSON.stringify(response));
        process.exit(1);
    }
}

main().catch(error => {
    console.error('Unexpected error:', error);
    process.exit(1);
});
