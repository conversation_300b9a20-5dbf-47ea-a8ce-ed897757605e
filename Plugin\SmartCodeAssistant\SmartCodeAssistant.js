const fs = require('fs');
const crypto = require('crypto');

class SmartCodeAssistant {
    constructor() {
        this.config = {};
        this.debugMode = false;
        this.supportedLanguages = [
            'javascript', 'python', 'java', 'cpp', 'c', 'csharp', 'php', 'ruby', 
            'go', 'rust', 'typescript', 'html', 'css', 'sql', 'shell'
        ];
    }

    log(message) {
        if (this.debugMode) {
            console.error(`[SmartCodeAssistant] ${message}`);
        }
    }

    loadConfig(configData) {
        try {
            let config = {};
            if (configData && configData.trim()) {
                try {
                    config = JSON.parse(configData);
                } catch (parseError) {
                    this.log(`Failed to parse config: ${parseError.message}`);
                }
            }

            this.config = config;
            this.debugMode = this.config.DEBUG_MODE === 'true' || this.config.DEBUG_MODE === true;
            
            this.log('Configuration loaded successfully');
        } catch (error) {
            throw new Error(`Failed to load configuration: ${error.message}`);
        }
    }

    detectLanguage(code) {
        // 简单的语言检测逻辑
        if (code.includes('def ') && code.includes(':')) return 'python';
        if (code.includes('function ') || code.includes('=>')) return 'javascript';
        if (code.includes('public class ') || code.includes('System.out.println')) return 'java';
        if (code.includes('#include') || code.includes('int main')) return 'cpp';
        if (code.includes('<?php')) return 'php';
        if (code.includes('SELECT') || code.includes('FROM')) return 'sql';
        return 'unknown';
    }

    analyzeCode(code, language, analysisType = 'all') {
        const analysis = {
            language: language || this.detectLanguage(code),
            lines: code.split('\n').length,
            characters: code.length,
            timestamp: new Date().toISOString()
        };

        if (analysisType === 'quality' || analysisType === 'all') {
            analysis.quality = this.analyzeQuality(code, analysis.language);
        }

        if (analysisType === 'performance' || analysisType === 'all') {
            analysis.performance = this.analyzePerformance(code, analysis.language);
        }

        if (analysisType === 'security' || analysisType === 'all') {
            analysis.security = this.analyzeSecurity(code, analysis.language);
        }

        return analysis;
    }

    analyzeQuality(code, language) {
        const issues = [];
        const suggestions = [];

        // 通用质量检查
        if (code.length > 10000) {
            issues.push('代码文件过大，建议拆分为多个模块');
        }

        const lines = code.split('\n');
        const longLines = lines.filter(line => line.length > 120);
        if (longLines.length > 0) {
            issues.push(`发现 ${longLines.length} 行代码过长（超过120字符）`);
        }

        // 语言特定检查
        switch (language) {
            case 'python':
                if (!code.includes('"""') && !code.includes("'''")) {
                    suggestions.push('建议添加文档字符串');
                }
                break;
            case 'javascript':
                if (code.includes('var ')) {
                    suggestions.push('建议使用 let/const 替代 var');
                }
                break;
        }

        return {
            score: Math.max(1, 10 - issues.length - suggestions.length),
            issues: issues,
            suggestions: suggestions
        };
    }

    analyzePerformance(code, language) {
        const issues = [];
        const suggestions = [];

        // 通用性能检查
        if (code.includes('for') && code.includes('for')) {
            const nestedLoops = (code.match(/for.*for/g) || []).length;
            if (nestedLoops > 0) {
                issues.push(`发现 ${nestedLoops} 个嵌套循环，可能影响性能`);
            }
        }

        // 语言特定性能检查
        switch (language) {
            case 'python':
                if (code.includes('list.append') && code.includes('for')) {
                    suggestions.push('考虑使用列表推导式提高性能');
                }
                break;
            case 'javascript':
                if (code.includes('document.getElementById') && code.includes('for')) {
                    suggestions.push('考虑缓存DOM查询结果');
                }
                break;
        }

        return {
            score: Math.max(1, 10 - issues.length),
            issues: issues,
            suggestions: suggestions
        };
    }

    analyzeSecurity(code, language) {
        const vulnerabilities = [];
        const warnings = [];

        // 通用安全检查
        if (code.includes('password') && !code.includes('hash')) {
            vulnerabilities.push('可能存在明文密码');
        }

        if (code.includes('eval(')) {
            vulnerabilities.push('使用 eval() 存在代码注入风险');
        }

        // 语言特定安全检查
        switch (language) {
            case 'sql':
                if (code.includes('SELECT') && code.includes('+')) {
                    vulnerabilities.push('可能存在SQL注入风险');
                }
                break;
            case 'php':
                if (code.includes('$_GET') || code.includes('$_POST')) {
                    warnings.push('直接使用用户输入，建议进行验证和过滤');
                }
                break;
        }

        return {
            risk_level: vulnerabilities.length > 0 ? 'high' : warnings.length > 0 ? 'medium' : 'low',
            vulnerabilities: vulnerabilities,
            warnings: warnings
        };
    }

    generateTests(code, language, testFramework, coverageLevel = 'comprehensive') {
        const functions = this.extractFunctions(code, language);
        let testCode = '';

        switch (language) {
            case 'python':
                testCode = this.generatePythonTests(functions, testFramework, coverageLevel);
                break;
            case 'javascript':
                testCode = this.generateJavaScriptTests(functions, testFramework, coverageLevel);
                break;
            case 'java':
                testCode = this.generateJavaTests(functions, testFramework, coverageLevel);
                break;
            default:
                testCode = `# 暂不支持 ${language} 的测试生成\n# 支持的语言: Python, JavaScript, Java`;
        }

        return {
            language: language,
            framework: testFramework,
            coverage_level: coverageLevel,
            test_code: testCode,
            functions_found: functions.length
        };
    }

    extractFunctions(code, language) {
        const functions = [];
        
        switch (language) {
            case 'python':
                const pythonMatches = code.match(/def\s+(\w+)\s*\([^)]*\):/g) || [];
                pythonMatches.forEach(match => {
                    const name = match.match(/def\s+(\w+)/)[1];
                    functions.push({ name, signature: match });
                });
                break;
            case 'javascript':
                const jsMatches = code.match(/function\s+(\w+)\s*\([^)]*\)/g) || [];
                jsMatches.forEach(match => {
                    const name = match.match(/function\s+(\w+)/)[1];
                    functions.push({ name, signature: match });
                });
                break;
        }

        return functions;
    }

    generatePythonTests(functions, framework = 'pytest', coverageLevel) {
        let testCode = `import pytest\nfrom your_module import *\n\n`;
        
        functions.forEach(func => {
            testCode += `def test_${func.name}():\n`;
            testCode += `    # 测试 ${func.name} 函数\n`;
            testCode += `    # TODO: 添加具体的测试用例\n`;
            testCode += `    assert ${func.name}() is not None\n\n`;
            
            if (coverageLevel === 'comprehensive') {
                testCode += `def test_${func.name}_edge_cases():\n`;
                testCode += `    # 测试边界情况\n`;
                testCode += `    # TODO: 添加边界测试用例\n`;
                testCode += `    pass\n\n`;
            }
        });

        return testCode;
    }

    generateJavaScriptTests(functions, framework = 'jest', coverageLevel) {
        let testCode = `const { ${functions.map(f => f.name).join(', ')} } = require('./your-module');\n\n`;
        
        functions.forEach(func => {
            testCode += `describe('${func.name}', () => {\n`;
            testCode += `  test('should work correctly', () => {\n`;
            testCode += `    // 测试 ${func.name} 函数\n`;
            testCode += `    expect(${func.name}()).toBeDefined();\n`;
            testCode += `  });\n`;
            
            if (coverageLevel === 'comprehensive') {
                testCode += `\n  test('should handle edge cases', () => {\n`;
                testCode += `    // 测试边界情况\n`;
                testCode += `    // TODO: 添加边界测试用例\n`;
                testCode += `  });\n`;
            }
            
            testCode += `});\n\n`;
        });

        return testCode;
    }

    generateJavaTests(functions, framework = 'junit', coverageLevel) {
        let testCode = `import org.junit.Test;\nimport static org.junit.Assert.*;\n\n`;
        testCode += `public class YourModuleTest {\n\n`;
        
        functions.forEach(func => {
            testCode += `    @Test\n`;
            testCode += `    public void test${func.name.charAt(0).toUpperCase() + func.name.slice(1)}() {\n`;
            testCode += `        // 测试 ${func.name} 方法\n`;
            testCode += `        // TODO: 添加具体的测试用例\n`;
            testCode += `        assertNotNull(${func.name}());\n`;
            testCode += `    }\n\n`;
        });
        
        testCode += `}\n`;
        return testCode;
    }

    optimizeCode(code, language, optimizationFocus = 'all') {
        const optimizations = [];
        let optimizedCode = code;

        switch (language) {
            case 'python':
                if (optimizationFocus === 'performance' || optimizationFocus === 'all') {
                    // 列表推导式优化
                    if (code.includes('for') && code.includes('append')) {
                        optimizations.push('建议使用列表推导式替代循环append');
                        optimizedCode = optimizedCode.replace(
                            /result = \[\]\s*for .+ in .+:\s*result\.append\(.+\)/g,
                            '# 优化建议: 使用列表推导式 [item for item in iterable]'
                        );
                    }
                }
                break;
            case 'javascript':
                if (optimizationFocus === 'performance' || optimizationFocus === 'all') {
                    // 使用现代JavaScript特性
                    if (code.includes('var ')) {
                        optimizations.push('使用 let/const 替代 var');
                        optimizedCode = optimizedCode.replace(/var /g, 'const ');
                    }
                }
                break;
        }

        return {
            original_lines: code.split('\n').length,
            optimized_lines: optimizedCode.split('\n').length,
            optimizations: optimizations,
            optimized_code: optimizedCode
        };
    }

    async processRequest(params) {
        const { command, code, language, analysis_type, test_framework, coverage_level, optimization_focus } = params;
        
        if (!code) {
            throw new Error('代码内容不能为空');
        }

        const detectedLanguage = language || this.detectLanguage(code);
        
        switch (command) {
            case 'analyze':
                const analysis = this.analyzeCode(code, detectedLanguage, analysis_type);
                return this.formatAnalysisReport(analysis);

            case 'generate_tests':
                const tests = this.generateTests(code, detectedLanguage, test_framework, coverage_level);
                return this.formatTestReport(tests);

            case 'optimize':
                const optimization = this.optimizeCode(code, detectedLanguage, optimization_focus);
                return this.formatOptimizationReport(optimization);

            case 'document':
                return this.generateDocumentation(code, detectedLanguage, params.doc_style);

            case 'security_scan':
                const security = this.analyzeSecurity(code, detectedLanguage);
                return this.formatSecurityReport(security);

            case 'refactor':
                return this.refactorCode(code, detectedLanguage, params.refactor_type);

            default:
                throw new Error(`未知命令: ${command}`);
        }
    }

    formatAnalysisReport(analysis) {
        let report = `📊 代码分析报告\n\n`;
        report += `🔤 语言: ${analysis.language}\n`;
        report += `📏 代码行数: ${analysis.lines}\n`;
        report += `📝 字符数: ${analysis.characters}\n`;
        report += `⏰ 分析时间: ${analysis.timestamp}\n\n`;

        if (analysis.quality) {
            report += `🎯 代码质量评分: ${analysis.quality.score}/10\n`;
            if (analysis.quality.issues.length > 0) {
                report += `❌ 发现问题:\n${analysis.quality.issues.map(i => `  • ${i}`).join('\n')}\n`;
            }
            if (analysis.quality.suggestions.length > 0) {
                report += `💡 改进建议:\n${analysis.quality.suggestions.map(s => `  • ${s}`).join('\n')}\n`;
            }
            report += '\n';
        }

        if (analysis.performance) {
            report += `⚡ 性能评分: ${analysis.performance.score}/10\n`;
            if (analysis.performance.issues.length > 0) {
                report += `⚠️ 性能问题:\n${analysis.performance.issues.map(i => `  • ${i}`).join('\n')}\n`;
            }
            if (analysis.performance.suggestions.length > 0) {
                report += `🚀 性能优化建议:\n${analysis.performance.suggestions.map(s => `  • ${s}`).join('\n')}\n`;
            }
            report += '\n';
        }

        if (analysis.security) {
            report += `🔒 安全风险级别: ${analysis.security.risk_level}\n`;
            if (analysis.security.vulnerabilities.length > 0) {
                report += `🚨 安全漏洞:\n${analysis.security.vulnerabilities.map(v => `  • ${v}`).join('\n')}\n`;
            }
            if (analysis.security.warnings.length > 0) {
                report += `⚠️ 安全警告:\n${analysis.security.warnings.map(w => `  • ${w}`).join('\n')}\n`;
            }
        }

        return report;
    }

    formatTestReport(tests) {
        let report = `🧪 测试代码生成报告\n\n`;
        report += `🔤 语言: ${tests.language}\n`;
        report += `🛠️ 测试框架: ${tests.framework}\n`;
        report += `📊 覆盖级别: ${tests.coverage_level}\n`;
        report += `🔍 发现函数: ${tests.functions_found} 个\n\n`;
        report += `📝 生成的测试代码:\n\n\`\`\`${tests.language}\n${tests.test_code}\n\`\`\``;
        
        return report;
    }

    formatOptimizationReport(optimization) {
        let report = `🚀 代码优化报告\n\n`;
        report += `📏 原始行数: ${optimization.original_lines}\n`;
        report += `📏 优化后行数: ${optimization.optimized_lines}\n`;
        report += `🔧 应用优化: ${optimization.optimizations.length} 项\n\n`;
        
        if (optimization.optimizations.length > 0) {
            report += `💡 优化项目:\n${optimization.optimizations.map(o => `  • ${o}`).join('\n')}\n\n`;
        }
        
        report += `📝 优化后代码:\n\n\`\`\`\n${optimization.optimized_code}\n\`\`\``;
        
        return report;
    }

    formatSecurityReport(security) {
        let report = `🔒 安全扫描报告\n\n`;
        report += `🚨 风险级别: ${security.risk_level.toUpperCase()}\n\n`;
        
        if (security.vulnerabilities.length > 0) {
            report += `🚨 发现安全漏洞:\n${security.vulnerabilities.map(v => `  • ${v}`).join('\n')}\n\n`;
        }
        
        if (security.warnings.length > 0) {
            report += `⚠️ 安全警告:\n${security.warnings.map(w => `  • ${w}`).join('\n')}\n\n`;
        }
        
        if (security.vulnerabilities.length === 0 && security.warnings.length === 0) {
            report += `✅ 未发现明显的安全问题\n`;
        }
        
        return report;
    }

    generateDocumentation(code, language, docStyle = 'auto') {
        return `📚 文档生成功能开发中...\n\n语言: ${language}\n文档风格: ${docStyle}\n\n将为您的代码生成详细的API文档和使用说明。`;
    }

    refactorCode(code, language, refactorType = 'general') {
        return `🔄 代码重构功能开发中...\n\n语言: ${language}\n重构类型: ${refactorType}\n\n将为您提供代码重构建议和自动重构功能。`;
    }
}

// 主执行逻辑
async function main() {
    const assistant = new SmartCodeAssistant();
    
    try {
        let inputData = '';
        process.stdin.setEncoding('utf8');
        
        for await (const chunk of process.stdin) {
            inputData += chunk;
        }
        
        if (!inputData.trim()) {
            throw new Error('No input data received');
        }

        const params = JSON.parse(inputData.trim());
        
        assistant.loadConfig(JSON.stringify(params.config || {}));
        
        const result = await assistant.processRequest(params);
        
        const response = {
            status: 'success',
            result: result
        };

        console.log(JSON.stringify(response));
        
    } catch (error) {
        const response = {
            status: 'error',
            error: error.message,
            messageForAI: `智能代码助手操作失败：${error.message}`
        };

        console.log(JSON.stringify(response));
        process.exit(1);
    }
}

main().catch(error => {
    console.error('Unexpected error:', error);
    process.exit(1);
});
