# 即梦AI插件快速开始指南

## 🚀 5分钟快速体验

### 第一步：确认插件文件

确保以下文件存在于 `Plugin/JimengAI/` 目录：

```
Plugin/JimengAI/
├── plugin-manifest.json    # 插件清单
├── JimengAI.js             # 主程序
├── config.env              # 配置文件
├── test.js                 # 测试脚本
└── README.md               # 说明文档
```

### 第二步：运行测试

直接运行测试脚本（使用默认的模拟模式）：

```bash
cd Plugin/JimengAI
node test.js
```

你应该看到类似输出：

```
🚀 即梦AI插件测试开始
✅ 1. 图片生成测试 - 通过
✅ 2. 视频生成测试 - 通过  
✅ 3. 数字人生成测试 - 通过
🎉 所有测试通过！
```

### 第三步：在VCP中使用

在VCP系统中，你可以这样调用插件：

#### 图片生成示例

```
<<<[TOOL_REQUEST]>>>
maid:「始」AI助手「末」,
tool_name:「始」JimengAI「末」,
type:「始」image「末」,
prompt:「始」一只可爱的小猫在花园里玩耍，阳光明媚，高清摄影风格「末」,
quality:「始」2k「末」,
style:「始」realistic「末」,
aspect_ratio:「始」16:9「末」
<<<[END_TOOL_REQUEST]>>>
```

#### 视频生成示例

```
<<<[TOOL_REQUEST]>>>
maid:「始」AI助手「末」,
tool_name:「始」JimengAI「末」,
type:「始」video「末」,
prompt:「始」海边日落，海浪轻拍沙滩，镜头缓慢推进「末」,
duration:「始」10「末」,
motion:「始」zoom_in「末」,
quality:「始」hd「末」
<<<[END_TOOL_REQUEST]>>>
```

#### 数字人生成示例

```
<<<[TOOL_REQUEST]>>>
maid:「始」AI助手「末」,
tool_name:「始」JimengAI「末」,
type:「始」digital_human「末」,
character:「始」年轻女性，长发，职业装，亲和力强「末」,
action:「始」挥手打招呼「末」,
expression:「始」微笑「末」,
background:「始」现代办公室「末」
<<<[END_TOOL_REQUEST]>>>
```

## 🎭 模拟模式 vs 真实API模式

### 当前状态（模拟模式）

- ✅ **立即可用**：无需任何配置
- ✅ **完整功能**：支持所有三种生成类型
- ✅ **标准响应**：返回符合VCP规范的响应格式
- ✅ **测试友好**：适合开发和测试

### 模拟模式响应示例

```json
{
  "status": "success",
  "result": {
    "success": true,
    "type": "image",
    "data": {
      "images": ["https://example.com/mock-image.jpg"],
      "model": "jimeng-3.0",
      "prompt": "一只可爱的小猫"
    },
    "message": "即梦AI图片生成成功（模拟模式）！..."
  }
}
```

## 🔧 升级到真实API

如果你想使用真实的即梦AI服务，有两种方案：

### 方案一：使用 jimeng-free-api（推荐）

1. **部署 jimeng-free-api 项目**：
   ```bash
   git clone https://github.com/LLM-Red-Team/jimeng-free-api
   cd jimeng-free-api
   npm install
   npm start
   ```

2. **修改配置**：
   ```env
   USE_REAL_API=true
   BASE_URL=http://localhost:8000
   SESSION_ID=你的会话ID
   ```

3. **获取Session ID**：
   - 访问 https://jimeng.jianying.com/
   - 登录账号
   - F12 → Application → Cookies → 找到 sessionid

### 方案二：直接集成

参考 `真实API使用指南.md` 进行详细配置。

## 📊 功能对比

| 功能 | 模拟模式 | 真实API模式 |
|------|----------|-------------|
| 图片生成 | ✅ 模拟响应 | ✅ 真实生成 |
| 视频生成 | ✅ 模拟响应 | ✅ 真实生成 |
| 数字人生成 | ✅ 模拟响应 | ✅ 真实生成 |
| 配置复杂度 | 🟢 无需配置 | 🟡 需要配置 |
| 响应速度 | 🟢 极快 | 🟡 取决于API |
| 成本 | 🟢 免费 | 🟡 消耗积分 |

## 🎯 使用建议

### 开发阶段
- 使用模拟模式进行功能测试
- 验证VCP调用格式和响应处理
- 调试插件逻辑和错误处理

### 生产环境
- 配置真实API获得实际生成结果
- 使用 jimeng-free-api 作为稳定的中间层
- 监控API调用频率和成本

## 🔍 故障排除

### 常见问题

1. **测试失败**
   ```bash
   # 检查Node.js版本
   node --version
   
   # 重新运行测试
   node test.js
   ```

2. **VCP调用失败**
   - 检查插件清单文件格式
   - 确认VCP系统已加载插件
   - 查看VCP系统日志

3. **配置问题**
   - 确认 config.env 文件存在
   - 检查配置项格式是否正确
   - 开启 DEBUG_MODE 查看详细日志

## 📚 更多资源

- 📖 [完整README](./README.md)
- 🔧 [真实API使用指南](./真实API使用指南.md)
- 🌐 [即梦AI官网](https://jimeng.jianying.com/)
- 🛠️ [jimeng-free-api项目](https://github.com/LLM-Red-Team/jimeng-free-api)

---

**恭喜！** 你已经成功配置了即梦AI插件。现在可以在VCP系统中享受强大的AI内容生成能力了！ 🎉
