# 📧 SmartReminder 邮件提醒配置指南

## 🚀 快速配置

### 1. Gmail 配置（推荐）

在 `config.env` 文件中设置：

```env
# 启用邮件通知
enable_email=true

# Gmail SMTP 配置
email_smtp_host=smtp.gmail.com
email_smtp_port=587
email_user=<EMAIL>
email_password=your-app-password
email_to=<EMAIL>
```

### 2. 获取 Gmail 应用密码

1. **登录 Google 账户**：https://myaccount.google.com/
2. **进入安全设置**：左侧菜单 → 安全性
3. **启用两步验证**（如果还没有启用）
4. **生成应用密码**：
   - 点击"应用密码"
   - 选择"邮件"和"Windows 计算机"
   - 点击"生成"
   - 复制生成的16位密码

### 3. 其他邮件服务商配置

#### QQ邮箱
```env
email_smtp_host=smtp.qq.com
email_smtp_port=587
email_user=<EMAIL>
email_password=your-authorization-code
```

#### 163邮箱
```env
email_smtp_host=smtp.163.com
email_smtp_port=587
email_user=<EMAIL>
email_password=your-authorization-code
```

#### Outlook/Hotmail
```env
email_smtp_host=smtp-mail.outlook.com
email_smtp_port=587
email_user=<EMAIL>
email_password=your-password
```

## 📝 使用方法

### 创建带邮件通知的提醒

```
<<<[TOOL_REQUEST]>>>
tool_name:「始」SmartReminder「末」,
command:「始」create「末」,
reminder_text:「始」重要会议提醒「末」,
time_expression:「始」明天下午3点「末」,
notification_methods:「始」email「末」
<<<[END_TOOL_REQUEST]>>>
```

### 同时使用 WebSocket 和邮件通知

```
<<<[TOOL_REQUEST]>>>
tool_name:「始」SmartReminder「末」,
command:「始」create「末」,
reminder_text:「始」重要会议提醒「末」,
time_expression:「始」明天下午3点「末」,
notification_methods:「始」websocket,email「末」
<<<[END_TOOL_REQUEST]>>>
```

## 🔧 测试邮件功能

### 测试命令

```bash
# 在插件目录下运行
echo '{"command":"create","reminder_text":"邮件测试","time_expression":"2分钟后","notification_methods":"email"}' | node smart_reminder_simple.js
```

### 预期结果

1. **立即收到确认邮件**：包含提醒设置详情
2. **到时间收到提醒邮件**：包含提醒内容

## 📧 邮件模板

### 确认邮件
- **主题**：📅 智能提醒已设置
- **内容**：提醒详情、时间、ID等信息
- **样式**：美观的HTML格式

### 提醒邮件
- **主题**：🔔 提醒通知
- **内容**：醒目的提醒内容
- **样式**：突出显示的HTML格式

## ⚠️ 常见问题

### 1. 邮件发送失败

**可能原因：**
- 邮箱密码错误（需要使用应用密码）
- SMTP服务器配置错误
- 网络连接问题
- 邮箱安全设置限制

**解决方法：**
- 检查应用密码是否正确
- 确认SMTP配置
- 检查防火墙设置
- 启用邮箱的SMTP服务

### 2. 收不到邮件

**检查项目：**
- 垃圾邮件文件夹
- 邮箱地址是否正确
- 邮件服务商限制

### 3. 中文乱码

**解决方法：**
- 确保邮件客户端支持UTF-8编码
- 检查系统区域设置

## 🔒 安全建议

1. **使用应用密码**：不要使用主密码
2. **定期更换密码**：建议每3-6个月更换
3. **限制权限**：只给必要的邮件权限
4. **监控使用**：定期检查邮件发送记录

## 📊 高级配置

### 自定义邮件模板

可以修改 `smart_reminder_simple.js` 中的邮件HTML模板来自定义样式。

### 多收件人

```env
email_to=<EMAIL>,<EMAIL>
```

### 邮件频率限制

为避免邮件轰炸，建议：
- 限制每小时邮件数量
- 合并相近时间的提醒
- 设置邮件发送间隔

## 🎯 最佳实践

1. **测试配置**：先用短时间提醒测试
2. **备用通知**：同时启用WebSocket和邮件
3. **重要提醒**：使用邮件确保不遗漏
4. **定期清理**：删除过期的提醒记录
