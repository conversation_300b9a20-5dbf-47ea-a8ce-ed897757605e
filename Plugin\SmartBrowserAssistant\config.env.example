# 智能浏览器助手配置文件示例
# 请复制此文件为 config.env 并填写您的实际配置

# ===== Chrome扩展连接配置 =====
# Chrome扩展WebSocket端口
CHROME_EXTENSION_PORT=8080

# ===== 操作配置 =====
# 自动滚动延迟（毫秒）
AUTO_SCROLL_DELAY=1000

# 最大等待时间（毫秒）
MAX_WAIT_TIME=30000

# 截图质量：low、medium、high
SCREENSHOT_QUALITY=medium

# 默认用户代理
DEFAULT_USER_AGENT=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36

# ===== 功能开关 =====
# 启用智能表单填写
ENABLE_SMART_FORMS=true

# 启用价格追踪
ENABLE_PRICE_TRACKING=true

# 启用内容提取
ENABLE_CONTENT_EXTRACTION=true

# ===== 调试配置 =====
# 调试模式
DEBUG_MODE=false

# ===== 高级配置 =====
# 请求重试次数
MAX_RETRIES=3

# 请求间延迟（毫秒）
REQUEST_DELAY=2000

# 监控检查间隔（秒）
MONITOR_CHECK_INTERVAL=60

# 价格追踪过期时间（小时）
PRICE_TRACKING_EXPIRE_HOURS=24
