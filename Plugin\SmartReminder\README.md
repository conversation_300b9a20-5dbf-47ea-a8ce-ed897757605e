# SmartReminder - 智能提醒助手

VCP 智能提醒助手插件，支持自然语言时间解析和多种提醒方式。

## 🌟 主要特性

- **自然语言时间解析**：支持"明天下午3点"、"下周一上午9点"等自然表达
- **多种提醒方式**：WebSocket推送、邮件通知
- **重复提醒**：支持一次性、每日、每周、每月重复
- **智能搜索**：快速查找相关提醒
- **时区支持**：自动处理时区转换

## 📦 安装依赖

在插件目录下运行：

```bash
npm install
```

## ⚙️ 配置

1. 复制 `config.env.example` 为 `config.env`
2. 根据需要修改配置项：

```env
# 基本配置
timezone=Asia/Shanghai
max_reminders=100

# 邮件通知配置（可选）
enable_email=true
email_smtp_host=smtp.gmail.com
email_smtp_port=587
email_user=<EMAIL>
email_password=your-app-password
```

## 🚀 使用方法

### 创建提醒

```
<<<[TOOL_REQUEST]>>>
tool_name:「始」SmartReminder「末」,
command:「始」create「末」,
reminder_text:「始」参加项目会议，记得准备PPT「末」,
time_expression:「始」明天下午3点「末」,
repeat_type:「始」once「末」,
notification_methods:「始」websocket「末」
<<<[END_TOOL_REQUEST]>>>
```

### 查看所有提醒

```
<<<[TOOL_REQUEST]>>>
tool_name:「始」SmartReminder「末」,
command:「始」list「末」
<<<[END_TOOL_REQUEST]>>>
```

### 搜索提醒

```
<<<[TOOL_REQUEST]>>>
tool_name:「始」SmartReminder「末」,
command:「始」search「末」,
keyword:「始」会议「末」
<<<[END_TOOL_REQUEST]>>>
```

### 删除提醒

```
<<<[TOOL_REQUEST]>>>
tool_name:「始」SmartReminder「末」,
command:「始」delete「末」,
reminder_id:「始」reminder_1234567890_abc123「末」
<<<[END_TOOL_REQUEST]>>>
```

## 🕐 支持的时间表达式

### 中文自然语言
- `明天上午9点`
- `后天下午2点30分`
- `下周一早上8点`
- `下周五晚上7点`
- `3天后`
- `2小时后`
- `30分钟后`

### 英文自然语言
- `tomorrow at 3pm`
- `next Monday at 9am`
- `in 2 hours`
- `in 30 minutes`

### 重复类型
- `once`: 一次性提醒
- `daily`: 每日重复
- `weekly`: 每周重复
- `monthly`: 每月重复

## 📱 通知方式

### WebSocket 推送
- 实时推送到前端应用
- 支持声音提醒
- 自动显示提醒内容

### 邮件通知
- 支持SMTP邮件发送
- 可配置多种邮件服务商
- 包含详细提醒信息

## 🔧 高级功能

### 批量操作
- 支持同时管理多个提醒
- 智能去重和冲突检测
- 提醒历史记录

### 智能解析
- 自动识别时间格式
- 处理模糊时间表达
- 时区自动转换

## 📊 数据存储

提醒数据存储在 `reminders.json` 文件中，包含：
- 提醒ID和内容
- 触发时间和重复设置
- 通知方式配置
- 创建和状态信息

## 🐛 故障排除

### 常见问题

1. **时间解析失败**
   - 检查时间表达式格式
   - 确认时区设置正确

2. **邮件发送失败**
   - 验证SMTP配置
   - 检查邮箱授权设置

3. **提醒未触发**
   - 确认系统时间正确
   - 检查提醒是否已过期

### 调试模式

在 `config.env` 中设置：
```env
DebugMode=true
```

## 📝 更新日志

### v1.0.0
- 初始版本发布
- 支持基本提醒功能
- 自然语言时间解析
- WebSocket和邮件通知

## 🤝 贡献

欢迎提交问题和改进建议！

## 📄 许可证

MIT License
