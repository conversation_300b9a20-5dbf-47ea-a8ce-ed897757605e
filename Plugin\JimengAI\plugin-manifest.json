{"manifestVersion": "1.0.0", "name": "JimengAI", "version": "1.0.0", "displayName": "即梦AI创作助手", "description": "调用即梦AI（剪映AI工具）进行图片生成、视频生成、数字人创作等AI内容创作。支持多种创作模式和高质量输出。", "author": "VCP Team", "pluginType": "asynchronous", "entryPoint": {"type": "nodejs", "command": "node JimengAI.js"}, "communication": {"protocol": "stdio", "timeout": 120000}, "webSocketPush": {"enabled": true, "messageType": "jimeng_ai_result", "usePluginResultAsMessage": true, "targetClientType": "VCPLog"}, "configSchema": {"SESSION_ID": "string", "BASE_URL": "string", "DEBUG_MODE": "boolean", "USE_REAL_API": "boolean", "REQUEST_TIMEOUT": "number", "MAX_RETRIES": "number", "REQUEST_INTERVAL": "number"}, "capabilities": {"invocationCommands": [{"commandIdentifier": "JimengImageGen", "description": "使用即梦AI生成图片。支持2K高清生成，所梦即所得的创作理念。请在您的回复中，使用以下精确格式来请求，确保所有参数值都用「始」和「末」准确包裹：\n<<<[TOOL_REQUEST]>>>\nmaid:「始」你的署名「末」,\ntool_name:「始」JimengAI「末」,\ntype:「始」image「末」,\nprompt:「始」图片描述提示词「末」,\nquality:「始」(可选) 图片质量，可选值：standard, hd, 2k，默认为hd「末」,\nstyle:「始」(可选) 图片风格，如：realistic, anime, artistic等「末」,\naspect_ratio:「始」(可选) 宽高比，如：1:1, 16:9, 9:16等，默认1:1「末」\n<<<[END_TOOL_REQUEST]>>>", "example": "生成高清图片:\n```text\n<<<[TOOL_REQUEST]>>>\nmaid:「始」AI助手「末」,\ntool_name:「始」JimengAI「末」,\ntype:「始」image「末」,\nprompt:「始」一只可爱的小猫在花园里玩耍，阳光明媚，高清摄影风格「末」,\nquality:「始」2k「末」,\nstyle:「始」realistic「末」,\naspect_ratio:「始」16:9「末」\n<<<[END_TOOL_REQUEST]>>>\n```"}, {"commandIdentifier": "JimengVideoGen", "description": "使用即梦AI生成视频。支持多镜头及运镜，精准响应用户需求。请在您的回复中，使用以下精确格式来请求：\n<<<[TOOL_REQUEST]>>>\nmaid:「始」你的署名「末」,\ntool_name:「始」JimengAI「末」,\ntype:「始」video「末」,\nprompt:「始」视频描述提示词「末」,\nduration:「始」(可选) 视频时长，单位秒，默认5秒「末」,\nmotion:「始」(可选) 运镜方式，如：zoom_in, zoom_out, pan_left, pan_right, static等「末」,\nquality:「始」(可选) 视频质量，可选值：standard, hd，默认hd「末」\n<<<[END_TOOL_REQUEST]>>>", "example": "生成视频:\n```text\n<<<[TOOL_REQUEST]>>>\nmaid:「始」AI助手「末」,\ntool_name:「始」JimengAI「末」,\ntype:「始」video「末」,\nprompt:「始」海边日落，海浪轻拍沙滩，镜头缓慢推进「末」,\nduration:「始」10「末」,\nmotion:「始」zoom_in「末」,\nquality:「始」hd「末」\n<<<[END_TOOL_REQUEST]>>>\n```"}, {"commandIdentifier": "JimengDigitalHuman", "description": "使用即梦AI创建数字人。大师模式，虚拟人物栩栩如生。请在您的回复中，使用以下精确格式来请求：\n<<<[TOOL_REQUEST]>>>\nmaid:「始」你的署名「末」,\ntool_name:「始」JimengAI「末」,\ntype:「始」digital_human「末」,\ncharacter:「始」数字人角色描述「末」,\naction:「始」(可选) 动作描述「末」,\nexpression:「始」(可选) 表情描述「末」,\nbackground:「始」(可选) 背景描述「末」\n<<<[END_TOOL_REQUEST]>>>", "example": "创建数字人:\n```text\n<<<[TOOL_REQUEST]>>>\nmaid:「始」AI助手「末」,\ntool_name:「始」JimengAI「末」,\ntype:「始」digital_human「末」,\ncharacter:「始」年轻女性，长发，职业装，亲和力强「末」,\naction:「始」挥手打招呼「末」,\nexpression:「始」微笑「末」,\nbackground:「始」现代办公室「末」\n<<<[END_TOOL_REQUEST]>>>\n```"}]}}