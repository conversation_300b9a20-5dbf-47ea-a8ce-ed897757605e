# 即梦AI插件 (JimengAI Plugin)

## 概述

即梦AI插件是VCP系统的一个异步插件，用于调用即梦AI（剪映AI工具）进行AI内容创作。支持图片生成、视频生成、数字人创作等多种功能。

**当前版本特性：**
- ✅ 模拟模式：完全可用，用于测试和演示
- 🔧 真实API模式：框架已就绪，需要配合 [jimeng-free-api](https://github.com/LLM-Red-Team/jimeng-free-api) 项目使用
- 📚 详细文档：提供完整的配置和使用指南

## 功能特性

### 🎨 图片生成
- 支持2K高清图片生成
- 多种风格选择（写实、动漫、艺术等）
- 自定义宽高比
- "所梦即所得"的创作理念

### 🎬 视频生成
- 支持多镜头及运镜
- 自定义视频时长
- 多种运镜方式（推拉摇移等）
- 高清视频输出

### 👤 数字人生成
- 大师模式数字人创作
- 自定义角色外观
- 动作和表情控制
- 背景场景设置

## 安装配置

### 1. 配置文件设置

编辑 `config.env` 文件：

```env
# 即梦AI会话ID（必需）
SESSION_ID=你的会话ID

# 工作模式（推荐先使用模拟模式测试）
USE_REAL_API=false

# 调试模式
DEBUG_MODE=true

# API基础URL
BASE_URL=https://jimeng.jianying.com
```

**重要**：默认使用模拟模式，可以直接测试插件功能。要使用真实API，请参考 `真实API使用指南.md`。

### 2. 获取Session ID

1. 访问 [即梦AI官网](https://jimeng.jianying.com/ai-tool/home)
2. 登录你的账号
3. 打开浏览器开发者工具 (F12)
4. 在Network或Application标签中查找session相关的cookie或请求头
5. 将获取到的session ID填入配置文件

## 使用方法

### 图片生成

```
<<<[TOOL_REQUEST]>>>
maid:「始」AI助手「末」,
tool_name:「始」JimengAI「末」,
type:「始」image「末」,
prompt:「始」一只可爱的小猫在花园里玩耍，阳光明媚，高清摄影风格「末」,
quality:「始」2k「末」,
style:「始」realistic「末」,
aspect_ratio:「始」16:9「末」
<<<[END_TOOL_REQUEST]>>>
```

**参数说明：**
- `prompt`: 图片描述提示词（必需）
- `quality`: 图片质量，可选值：standard, hd, 2k（可选，默认hd）
- `style`: 图片风格，如：realistic, anime, artistic（可选）
- `aspect_ratio`: 宽高比，如：1:1, 16:9, 9:16（可选，默认1:1）

### 视频生成

```
<<<[TOOL_REQUEST]>>>
maid:「始」AI助手「末」,
tool_name:「始」JimengAI「末」,
type:「始」video「末」,
prompt:「始」海边日落，海浪轻拍沙滩，镜头缓慢推进「末」,
duration:「始」10「末」,
motion:「始」zoom_in「末」,
quality:「始」hd「末」
<<<[END_TOOL_REQUEST]>>>
```

**参数说明：**
- `prompt`: 视频描述提示词（必需）
- `duration`: 视频时长，单位秒（可选，默认5秒）
- `motion`: 运镜方式，如：zoom_in, zoom_out, pan_left, pan_right, static（可选）
- `quality`: 视频质量，可选值：standard, hd（可选，默认hd）

### 数字人生成

```
<<<[TOOL_REQUEST]>>>
maid:「始」AI助手「末」,
tool_name:「始」JimengAI「末」,
type:「始」digital_human「末」,
character:「始」年轻女性，长发，职业装，亲和力强「末」,
action:「始」挥手打招呼「末」,
expression:「始」微笑「末」,
background:「始」现代办公室「末」
<<<[END_TOOL_REQUEST]>>>
```

**参数说明：**
- `character`: 数字人角色描述（必需）
- `action`: 动作描述（可选）
- `expression`: 表情描述（可选）
- `background`: 背景描述（可选）

## 配置参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| SESSION_ID | string | - | 即梦AI会话ID（必需） |
| BASE_URL | string | https://jimeng.jianying.com | API基础URL |
| DEBUG_MODE | boolean | false | 调试模式 |
| REQUEST_TIMEOUT | number | 120000 | 请求超时时间（毫秒） |
| MAX_RETRIES | number | 3 | 最大重试次数 |
| REQUEST_INTERVAL | number | 1000 | 请求间隔（毫秒） |

## 工作模式

### 🎭 模拟模式（默认）

- **优点**: 无需配置，立即可用，用于测试和演示
- **功能**: 返回模拟的生成结果，包含完整的响应格式
- **配置**: `USE_REAL_API=false`

### 🚀 真实API模式

- **优点**: 调用真实的即梦AI服务，生成实际内容
- **要求**: 需要有效的Session ID和正确的API配置
- **推荐方案**: 使用 [jimeng-free-api](https://github.com/LLM-Red-Team/jimeng-free-api) 项目作为中间层
- **配置**: `USE_REAL_API=true`

详细配置方法请参考 `真实API使用指南.md`。

## 注意事项

1. **会话有效性**: Session ID有时效性，如果出现认证错误，请重新获取
2. **请求频率**: 避免过于频繁的请求，建议设置适当的请求间隔
3. **异步处理**: 视频和数字人生成是异步任务，需要等待一定时间
4. **网络环境**: 确保网络连接稳定，某些功能可能需要特定的网络环境
5. **模式选择**: 建议先使用模拟模式测试，确认功能正常后再配置真实API

## 错误处理

插件会自动处理常见错误：
- 网络连接错误
- API认证失败
- 参数验证错误
- 超时错误

如遇到问题，请检查：
1. Session ID是否正确且有效
2. 网络连接是否正常
3. 参数格式是否正确
4. 查看调试日志（开启DEBUG_MODE）

## 版本信息

- 版本: 1.0.0
- 作者: VCP Team
- 插件类型: 异步插件
- 支持的VCP版本: 1.0+

## 更新日志

### v1.0.0 (2024-01-XX)
- 初始版本发布
- 支持图片生成功能
- 支持视频生成功能
- 支持数字人生成功能
- 完整的错误处理和日志记录
