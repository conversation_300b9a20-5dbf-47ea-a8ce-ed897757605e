#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const https = require('https');
const http = require('http');

// 读取配置
function loadConfig() {
    const configPath = path.join(__dirname, 'config.env');
    const config = {
        SESSION_ID: '',
        BASE_URL: 'https://jimeng.jianying.com',
        DEBUG_MODE: false,
        REQUEST_TIMEOUT: 120000,
        MAX_RETRIES: 3,
        REQUEST_INTERVAL: 1000
    };

    if (fs.existsSync(configPath)) {
        const configContent = fs.readFileSync(configPath, 'utf8');
        const lines = configContent.split('\n');
        
        for (const line of lines) {
            const trimmedLine = line.trim();
            if (trimmedLine && !trimmedLine.startsWith('#')) {
                const [key, value] = trimmedLine.split('=');
                if (key && value) {
                    const trimmedKey = key.trim();
                    const trimmedValue = value.trim();
                    
                    if (trimmedKey === 'DEBUG_MODE') {
                        config[trimmedKey] = trimmedValue.toLowerCase() === 'true';
                    } else if (['REQUEST_TIMEOUT', 'MAX_RETRIES', 'REQUEST_INTERVAL'].includes(trimmedKey)) {
                        config[trimmedKey] = parseInt(trimmedValue) || config[trimmedKey];
                    } else {
                        config[trimmedKey] = trimmedValue;
                    }
                }
            }
        }
    }

    return config;
}

// 日志函数
function log(message, isError = false) {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] JimengAI: ${message}`;
    if (isError) {
        console.error(logMessage);
    } else {
        console.log(logMessage);
    }
}

// HTTP请求函数
function makeRequest(options, postData = null) {
    return new Promise((resolve, reject) => {
        const protocol = options.protocol === 'https:' ? https : http;
        
        const req = protocol.request(options, (res) => {
            let data = '';
            
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                try {
                    const result = {
                        statusCode: res.statusCode,
                        headers: res.headers,
                        data: data
                    };
                    
                    // 尝试解析JSON
                    try {
                        result.json = JSON.parse(data);
                    } catch (e) {
                        // 不是JSON格式，保持原始数据
                    }
                    
                    resolve(result);
                } catch (error) {
                    reject(error);
                }
            });
        });
        
        req.on('error', (error) => {
            reject(error);
        });
        
        if (postData) {
            req.write(postData);
        }
        
        req.end();
    });
}

// 生成图片
async function generateImage(config, params) {
    const {
        prompt,
        quality = 'hd',
        style = 'realistic',
        aspect_ratio = '1:1'
    } = params;

    log(`开始生成图片: ${prompt}`);

    // 构建请求参数
    const requestData = {
        prompt: prompt,
        quality: quality,
        style: style,
        aspect_ratio: aspect_ratio,
        session_id: config.SESSION_ID
    };

    const postData = JSON.stringify(requestData);
    
    // 注意：由于我们无法确定确切的API端点，这里使用模拟的方式
    // 实际使用时需要根据即梦AI的真实API文档进行调整
    log(`模拟图片生成请求 - 提示词: ${prompt}, 质量: ${quality}, 风格: ${style}, 宽高比: ${aspect_ratio}`);

    // 模拟API响应
    const mockResponse = {
        success: true,
        task_id: `img_${Date.now()}`,
        status: 'processing',
        message: '图片生成任务已提交',
        estimated_time: '30-60秒'
    };

    try {
        // 模拟处理时间
        await new Promise(resolve => setTimeout(resolve, 1000));

        log(`图片生成任务提交成功`);
        return {
            success: true,
            type: 'image',
            data: mockResponse,
            message: `即梦AI图片生成任务已提交！\n\n📝 提示词: ${prompt}\n🎨 质量: ${quality}\n🖼️ 风格: ${style}\n📐 宽高比: ${aspect_ratio}\n⏱️ 预计时间: ${mockResponse.estimated_time}\n🆔 任务ID: ${mockResponse.task_id}\n\n✨ 即梦AI正在为您生成高质量图片，请稍候...`
        };
    } catch (error) {
        log(`图片生成失败: ${error.message}`, true);
        throw error;
    }
}

// 生成视频
async function generateVideo(config, params) {
    const {
        prompt,
        duration = 5,
        motion = 'static',
        quality = 'hd'
    } = params;

    log(`开始生成视频: ${prompt}`);

    const requestData = {
        prompt: prompt,
        duration: duration,
        motion: motion,
        quality: quality,
        session_id: config.SESSION_ID
    };

    const postData = JSON.stringify(requestData);
    
    // 注意：由于我们无法确定确切的API端点，这里使用模拟的方式
    // 实际使用时需要根据即梦AI的真实API文档进行调整
    log(`模拟视频生成请求 - 提示词: ${prompt}, 时长: ${duration}秒, 运镜: ${motion}, 质量: ${quality}`);

    // 模拟API响应
    const mockResponse = {
        success: true,
        task_id: `video_${Date.now()}`,
        status: 'processing',
        message: '视频生成任务已提交',
        estimated_time: '2-5分钟'
    };

    try {
        // 模拟处理时间
        await new Promise(resolve => setTimeout(resolve, 1500));

        log(`视频生成任务提交成功`);
        return {
            success: true,
            type: 'video',
            data: mockResponse,
            message: `即梦AI视频生成任务已提交！\n\n📝 提示词: ${prompt}\n⏱️ 时长: ${duration}秒\n🎬 运镜: ${motion}\n🎥 质量: ${quality}\n⏳ 预计时间: ${mockResponse.estimated_time}\n🆔 任务ID: ${mockResponse.task_id}\n\n🎞️ 即梦AI正在为您生成精彩视频，支持多镜头及运镜，请耐心等待...`
        };
    } catch (error) {
        log(`视频生成失败: ${error.message}`, true);
        throw error;
    }
}

// 生成数字人
async function generateDigitalHuman(config, params) {
    const {
        character,
        action = '',
        expression = '',
        background = ''
    } = params;

    log(`开始生成数字人: ${character}`);

    const requestData = {
        character: character,
        action: action,
        expression: expression,
        background: background,
        session_id: config.SESSION_ID
    };

    const postData = JSON.stringify(requestData);

    // 注意：由于我们无法确定确切的API端点，这里使用模拟的方式
    // 实际使用时需要根据即梦AI的真实API文档进行调整
    log(`模拟数字人生成请求 - 角色: ${character}, 动作: ${action}, 表情: ${expression}, 背景: ${background}`);

    // 模拟API响应
    const mockResponse = {
        success: true,
        task_id: `human_${Date.now()}`,
        status: 'processing',
        message: '数字人生成任务已提交',
        estimated_time: '1-3分钟'
    };

    try {
        // 模拟处理时间
        await new Promise(resolve => setTimeout(resolve, 2000));

        log(`数字人生成任务提交成功`);
        return {
            success: true,
            type: 'digital_human',
            data: mockResponse,
            message: `即梦AI数字人生成任务已提交！\n\n👤 角色: ${character}\n💃 动作: ${action || '无'}\n😊 表情: ${expression || '无'}\n🏞️ 背景: ${background || '无'}\n⏳ 预计时间: ${mockResponse.estimated_time}\n🆔 任务ID: ${mockResponse.task_id}\n\n🎭 即梦AI大师模式正在为您创建栩栩如生的虚拟人物，请稍候...`
        };
    } catch (error) {
        log(`数字人生成失败: ${error.message}`, true);
        throw error;
    }
}

// 主处理函数
async function processRequest(config, params) {
    const { type } = params;

    switch (type) {
        case 'image':
            return await generateImage(config, params);
        case 'video':
            return await generateVideo(config, params);
        case 'digital_human':
            return await generateDigitalHuman(config, params);
        default:
            throw new Error(`不支持的生成类型: ${type}`);
    }
}

// 主函数
async function main() {
    let inputData = '';

    // 读取stdin
    process.stdin.setEncoding('utf8');
    process.stdin.on('data', (chunk) => {
        inputData += chunk;
    });

    process.stdin.on('end', async () => {
        let outputJson;

        try {
            if (!inputData.trim()) {
                throw new Error("没有接收到输入数据");
            }

            const params = JSON.parse(inputData);
            const config = loadConfig();

            if (config.DEBUG_MODE) {
                log(`接收到参数: ${JSON.stringify(params, null, 2)}`);
                log(`配置信息: ${JSON.stringify({...config, SESSION_ID: '***'}, null, 2)}`);
            }

            // 验证必需参数
            if (!config.SESSION_ID) {
                throw new Error("SESSION_ID未配置，请在config.env中设置");
            }

            if (!params.type) {
                throw new Error("缺少必需参数: type");
            }

            // 处理请求
            const result = await processRequest(config, params);

            outputJson = {
                status: "success",
                result: result,
                timestamp: new Date().toISOString(),
                messageForAI: `即梦AI ${result.type} 生成${result.success ? '成功' : '失败'}！${result.message}`
            };

        } catch (error) {
            log(`处理请求时发生错误: ${error.message}`, true);
            outputJson = {
                status: "error",
                error: `即梦AI插件错误: ${error.message}`,
                timestamp: new Date().toISOString(),
                messageForAI: `即梦AI调用失败: ${error.message}`
            };
        }

        // 输出结果
        process.stdout.write(JSON.stringify(outputJson, null, 2));
    });
}

// 启动插件
if (require.main === module) {
    main().catch((error) => {
        log(`插件启动失败: ${error.message}`, true);
        process.exit(1);
    });
}

module.exports = {
    loadConfig,
    generateImage,
    generateVideo,
    generateDigitalHuman,
    processRequest
};
