#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const https = require('https');
const http = require('http');
const zlib = require('zlib');

// 读取配置
function loadConfig() {
    const configPath = path.join(__dirname, 'config.env');
    const config = {
        SESSION_ID: '',
        BASE_URL: 'https://jimeng.jianying.com',
        DEBUG_MODE: false,
        REQUEST_TIMEOUT: 120000,
        MAX_RETRIES: 3,
        REQUEST_INTERVAL: 1000
    };

    if (fs.existsSync(configPath)) {
        const configContent = fs.readFileSync(configPath, 'utf8');
        const lines = configContent.split('\n');
        
        for (const line of lines) {
            const trimmedLine = line.trim();
            if (trimmedLine && !trimmedLine.startsWith('#')) {
                const [key, value] = trimmedLine.split('=');
                if (key && value) {
                    const trimmedKey = key.trim();
                    const trimmedValue = value.trim();
                    
                    if (trimmedKey === 'DEBUG_MODE') {
                        config[trimmedKey] = trimmedValue.toLowerCase() === 'true';
                    } else if (['REQUEST_TIMEOUT', 'MAX_RETRIES', 'REQUEST_INTERVAL'].includes(trimmedKey)) {
                        config[trimmedKey] = parseInt(trimmedValue) || config[trimmedKey];
                    } else {
                        config[trimmedKey] = trimmedValue;
                    }
                }
            }
        }
    }

    return config;
}

// 日志函数
function log(message, isError = false) {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] JimengAI: ${message}`;
    if (isError) {
        console.error(logMessage);
    } else {
        console.log(logMessage);
    }
}

// HTTP请求函数
function makeRequest(options, postData = null) {
    return new Promise((resolve, reject) => {
        const protocol = options.protocol === 'https:' ? https : http;

        const req = protocol.request(options, (res) => {
            let data = Buffer.alloc(0);

            res.on('data', (chunk) => {
                data = Buffer.concat([data, chunk]);
            });

            res.on('end', () => {
                try {
                    let responseData = data;

                    // 处理gzip压缩
                    if (res.headers['content-encoding'] === 'gzip') {
                        responseData = zlib.gunzipSync(data);
                    } else if (res.headers['content-encoding'] === 'deflate') {
                        responseData = zlib.inflateSync(data);
                    } else if (res.headers['content-encoding'] === 'br') {
                        responseData = zlib.brotliDecompressSync(data);
                    }

                    const dataString = responseData.toString('utf8');

                    const result = {
                        statusCode: res.statusCode,
                        headers: res.headers,
                        data: dataString
                    };

                    // 尝试解析JSON
                    try {
                        result.json = JSON.parse(dataString);
                    } catch (e) {
                        // 不是JSON格式，保持原始数据
                    }

                    resolve(result);
                } catch (error) {
                    reject(error);
                }
            });
        });

        req.on('error', (error) => {
            reject(error);
        });

        req.setTimeout(options.timeout || 120000, () => {
            req.destroy();
            reject(new Error('请求超时'));
        });

        if (postData) {
            req.write(postData);
        }

        req.end();
    });
}

// 生成图片
async function generateImage(config, params) {
    const {
        prompt,
        quality = 'hd',
        style = 'realistic',
        aspect_ratio = '1:1'
    } = params;

    log(`开始生成图片: ${prompt}`);

    // 检查是否使用真实API
    if (config.USE_REAL_API === 'false' || config.USE_REAL_API === false) {
        // 模拟模式
        log(`模拟模式 - 图片生成: ${prompt}`);
        await new Promise(resolve => setTimeout(resolve, 2000));

        return {
            success: true,
            type: 'image',
            data: {
                images: ['https://example.com/mock-image.jpg'],
                model: 'jimeng-3.0',
                prompt: prompt
            },
            message: `即梦AI图片生成成功（模拟模式）！\n\n📝 提示词: ${prompt}\n🎨 质量: ${quality}\n🖼️ 风格: ${style}\n📐 宽高比: ${aspect_ratio}\n\n✨ 这是模拟模式的结果，要使用真实API请设置 USE_REAL_API=true`
        };
    }

    // 真实API调用模式
    // 由于即梦AI的API端点可能需要特殊的认证或者不对外开放
    // 这里我们提供一个框架，用户可以根据实际情况调整

    log(`真实API模式暂未完全实现，建议使用 jimeng-free-api 项目作为中间层`);
    log(`项目地址: https://github.com/LLM-Red-Team/jimeng-free-api`);

    return {
        success: false,
        type: 'image',
        data: {
            error: 'API_NOT_IMPLEMENTED',
            suggestion: 'jimeng-free-api'
        },
        message: `即梦AI真实API调用暂未完全实现！\n\n💡 建议方案：\n1. 部署 jimeng-free-api 项目作为中间层\n2. 项目地址: https://github.com/LLM-Red-Team/jimeng-free-api\n3. 或者设置 USE_REAL_API=false 使用模拟模式\n\n📝 提示词: ${prompt}\n🎨 质量: ${quality}\n🖼️ 风格: ${style}\n📐 宽高比: ${aspect_ratio}`
    };
}

// 生成视频
async function generateVideo(config, params) {
    const {
        prompt,
        duration = 5,
        motion = 'static',
        quality = 'hd'
    } = params;

    log(`开始生成视频: ${prompt}`);

    // 检查是否使用真实API
    if (config.USE_REAL_API === 'false' || config.USE_REAL_API === false) {
        // 模拟模式
        log(`模拟模式 - 视频生成: ${prompt}`);
        await new Promise(resolve => setTimeout(resolve, 3000));

        return {
            success: true,
            type: 'video',
            data: {
                video_url: 'https://example.com/mock-video.mp4',
                model: 'jimeng-3.0',
                prompt: prompt,
                duration: duration,
                motion: motion
            },
            message: `即梦AI视频生成成功（模拟模式）！\n\n📝 提示词: ${prompt}\n⏱️ 时长: ${duration}秒\n🎬 运镜: ${motion}\n🎥 质量: ${quality}\n\n✨ 这是模拟模式的结果，要使用真实API请设置 USE_REAL_API=true`
        };
    }

    // 真实API调用模式
    log(`真实API模式暂未完全实现，建议使用 jimeng-free-api 项目作为中间层`);

    return {
        success: false,
        type: 'video',
        data: {
            error: 'API_NOT_IMPLEMENTED',
            suggestion: 'jimeng-free-api'
        },
        message: `即梦AI视频生成真实API调用暂未完全实现！\n\n💡 建议方案：\n1. 部署 jimeng-free-api 项目作为中间层\n2. 项目地址: https://github.com/LLM-Red-Team/jimeng-free-api\n3. 或者设置 USE_REAL_API=false 使用模拟模式\n\n📝 提示词: ${prompt}\n⏱️ 时长: ${duration}秒\n🎬 运镜: ${motion}`
    };
}

// 生成数字人
async function generateDigitalHuman(config, params) {
    const {
        character,
        action = '',
        expression = '',
        background = ''
    } = params;

    log(`开始生成数字人: ${character}`);

    // 检查是否使用真实API
    if (config.USE_REAL_API === 'false' || config.USE_REAL_API === false) {
        // 模拟模式
        log(`模拟模式 - 数字人生成: ${character}`);
        await new Promise(resolve => setTimeout(resolve, 4000));

        return {
            success: true,
            type: 'digital_human',
            data: {
                images: ['https://example.com/mock-digital-human.jpg'],
                model: 'jimeng-3.0',
                character: character,
                action: action,
                expression: expression,
                background: background
            },
            message: `即梦AI数字人生成成功（模拟模式）！\n\n👤 角色: ${character}\n💃 动作: ${action || '无'}\n😊 表情: ${expression || '无'}\n🏞️ 背景: ${background || '无'}\n\n✨ 这是模拟模式的结果，要使用真实API请设置 USE_REAL_API=true`
        };
    }

    // 真实API调用模式
    log(`真实API模式暂未完全实现，建议使用 jimeng-free-api 项目作为中间层`);

    return {
        success: false,
        type: 'digital_human',
        data: {
            error: 'API_NOT_IMPLEMENTED',
            suggestion: 'jimeng-free-api'
        },
        message: `即梦AI数字人生成真实API调用暂未完全实现！\n\n💡 建议方案：\n1. 部署 jimeng-free-api 项目作为中间层\n2. 项目地址: https://github.com/LLM-Red-Team/jimeng-free-api\n3. 或者设置 USE_REAL_API=false 使用模拟模式\n\n👤 角色: ${character}\n💃 动作: ${action || '无'}\n😊 表情: ${expression || '无'}`
    };

}

// 主处理函数
async function processRequest(config, params) {
    const { type } = params;

    switch (type) {
        case 'image':
            return await generateImage(config, params);
        case 'video':
            return await generateVideo(config, params);
        case 'digital_human':
            return await generateDigitalHuman(config, params);
        default:
            throw new Error(`不支持的生成类型: ${type}`);
    }
}

// 主函数
async function main() {
    let inputData = '';

    // 读取stdin
    process.stdin.setEncoding('utf8');
    process.stdin.on('data', (chunk) => {
        inputData += chunk;
    });

    process.stdin.on('end', async () => {
        let outputJson;

        try {
            if (!inputData.trim()) {
                throw new Error("没有接收到输入数据");
            }

            const params = JSON.parse(inputData);
            const config = loadConfig();

            if (config.DEBUG_MODE) {
                log(`接收到参数: ${JSON.stringify(params, null, 2)}`);
                log(`配置信息: ${JSON.stringify({...config, SESSION_ID: '***'}, null, 2)}`);
            }

            // 验证必需参数
            if (!config.SESSION_ID) {
                throw new Error("SESSION_ID未配置，请在config.env中设置");
            }

            if (!params.type) {
                throw new Error("缺少必需参数: type");
            }

            // 处理请求
            const result = await processRequest(config, params);

            outputJson = {
                status: "success",
                result: result,
                timestamp: new Date().toISOString(),
                messageForAI: `即梦AI ${result.type} 生成${result.success ? '成功' : '失败'}！${result.message}`
            };

        } catch (error) {
            log(`处理请求时发生错误: ${error.message}`, true);
            outputJson = {
                status: "error",
                error: `即梦AI插件错误: ${error.message}`,
                timestamp: new Date().toISOString(),
                messageForAI: `即梦AI调用失败: ${error.message}`
            };
        }

        // 输出结果
        process.stdout.write(JSON.stringify(outputJson, null, 2));
    });
}

// 启动插件
if (require.main === module) {
    main().catch((error) => {
        log(`插件启动失败: ${error.message}`, true);
        process.exit(1);
    });
}

module.exports = {
    loadConfig,
    generateImage,
    generateVideo,
    generateDigitalHuman,
    processRequest
};
