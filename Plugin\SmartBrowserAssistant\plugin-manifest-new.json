{"manifestVersion": "1.0.0", "name": "SmartBrowserAssistant", "displayName": "智能浏览器助手", "version": "1.0.0", "description": "高级浏览器自动化系统，在ChromeControl基础上提供智能表单填写、购物助手、数据提取、复杂工作流等高级功能。与ChromeControl/ChromeObserver形成完美互补。", "author": "VCP Browser Team", "pluginType": "synchronous", "entryPoint": {"type": "nodejs", "command": "node SmartBrowserAssistant.js"}, "communication": {"protocol": "stdio", "timeout": 180000}, "configSchema": {"DebugMode": {"type": "boolean", "description": "是否启用详细的调试日志输出到stderr", "default": false, "required": false}}, "capabilities": {"invocationCommands": [{"command": "analyze_page", "description": "智能分析当前网页内容，提取关键信息并生成结构化报告。\n\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」SmartBrowserAssistant「末」,\ncommand:「始」analyze_page「末」,\nanalysis_type:「始」(可选) 分析类型：content(内容)、structure(结构)、seo(SEO)、accessibility(可访问性)、all(全部)。默认：content「末」,\nextract_images:「始」(可选) 是否提取图片信息，true/false。默认：false「末」,\nextract_links:「始」(可选) 是否提取链接信息，true/false。默认：true「末」,\nsummarize:「始」(可选) 是否生成页面摘要，true/false。默认：true「末」\n<<<[END_TOOL_REQUEST]>>>\n\n功能：智能分析网页内容，提取关键信息并生成结构化报告。"}, {"command": "fill_form", "description": "智能表单填写助手，基于上下文和用户配置自动填写表单。\n\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」SmartBrowserAssistant「末」,\ncommand:「始」fill_form「末」,\nform_data:「始」(必需) 表单数据，JSON格式：{\"field_name\": \"value\"}「末」,\nsubmit_form:「始」(可选) 填写后是否提交表单，true/false。默认：false「末」,\nvalidate_before_submit:「始」(可选) 提交前是否验证，true/false。默认：true「末」,\nform_selector:「始」(可选) 表单选择器，如果页面有多个表单「末」\n<<<[END_TOOL_REQUEST]>>>\n\n功能：智能表单填写助手，支持自动填写和验证。"}, {"command": "smart_shopping", "description": "智能购物助手，支持商品搜索、价格比较、自动加购物车等功能。\n\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」SmartBrowserAssistant「末」,\ncommand:「始」smart_shopping「末」,\nshopping_action:「始」(必需) 购物操作：search(搜索)、compare_prices(比价)、add_to_cart(加购物车)、track_price(价格追踪)「末」,\nproduct_name:「始」(可选) 商品名称「末」,\nmax_price:「始」(可选) 最高价格「末」,\nmin_rating:「始」(可选) 最低评分「末」,\nstore_preference:「始」(可选) 商店偏好，用逗号分隔「末」\n<<<[END_TOOL_REQUEST]>>>\n\n功能：智能购物助手，支持商品搜索和价格比较。"}, {"command": "extract_content", "description": "智能内容提取器，从网页中提取特定类型的内容。\n\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」SmartBrowserAssistant「末」,\ncommand:「始」extract_content「末」,\ncontent_type:「始」(必需) 内容类型：text(文本)、images(图片)、videos(视频)、tables(表格)、contacts(联系信息)、prices(价格)、reviews(评论)「末」,\nfilter_criteria:「始」(可选) 过滤条件「末」,\nformat_output:「始」(可选) 输出格式：json、markdown、csv。默认：markdown「末」,\nmax_items:「始」(可选) 最大提取数量。默认：50「末」\n<<<[END_TOOL_REQUEST]>>>\n\n功能：智能内容提取器，支持多种内容类型和输出格式。"}, {"command": "run_workflow", "description": "执行复杂的自动化工作流，支持多步骤操作和条件判断。\n\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」SmartBrowserAssistant「末」,\ncommand:「始」run_workflow「末」,\nworkflow_steps:「始」(必需) 工作流步骤，JSON数组格式「末」,\nerror_handling:「始」(可选) 错误处理策略：stop(停止)、skip(跳过)、retry(重试)。默认：stop「末」,\nmax_retries:「始」(可选) 最大重试次数。默认：3「末」,\nstep_delay:「始」(可选) 步骤间延迟（毫秒）。默认：1000「末」\n<<<[END_TOOL_REQUEST]>>>\n\n功能：执行复杂的自动化工作流，支持多步骤操作。"}, {"command": "monitor_page", "description": "监控网页变化，当指定内容发生变化时发送通知。\n\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」SmartBrowserAssistant「末」,\ncommand:「始」monitor_page「末」,\nmonitor_type:「始」(必需) 监控类型：content_change(内容变化)、price_change(价格变化)、new_elements(新元素)、element_removal(元素移除)「末」,\ntarget_selector:「始」(可选) 目标元素选择器「末」,\ncheck_interval:「始」(可选) 检查间隔（秒）。默认：60「末」,\nnotification_method:「始」(可选) 通知方式：console、webhook、email。默认：console「末」,\nstop_condition:「始」(可选) 停止条件「末」\n<<<[END_TOOL_REQUEST]>>>\n\n功能：监控网页变化，支持多种监控类型和通知方式。"}, {"command": "smart_navigate", "description": "智能导航助手，基于自然语言描述导航到目标页面或元素。\n\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」SmartBrowserAssistant「末」,\ncommand:「始」smart_navigate「末」,\nnavigation_goal:「始」(必需) 导航目标的自然语言描述「末」,\nstart_url:「始」(可选) 起始URL，如果不是当前页面「末」,\nmax_steps:「始」(可选) 最大导航步数。默认：10「末」,\nuse_search:「始」(可选) 是否使用搜索引擎辅助，true/false。默认：true「末」\n<<<[END_TOOL_REQUEST]>>>\n\n功能：智能导航助手，基于自然语言描述进行导航。"}, {"command": "scrape_data", "description": "高级数据抓取器，支持复杂的数据提取和结构化输出。\n\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」SmartBrowserAssistant「末」,\ncommand:「始」scrape_data「末」,\nscrape_config:「始」(必需) 抓取配置，JSON格式：{\"fields\": [{\"name\": \"title\", \"selector\": \"h1\"}]}「末」,\noutput_format:「始」(可选) 输出格式：json、csv、excel。默认：json「末」,\nhandle_pagination:「始」(可选) 是否处理分页，true/false。默认：false「末」,\nmax_pages:「始」(可选) 最大抓取页数。默认：5「末」,\ndelay_between_requests:「始」(可选) 请求间延迟（毫秒）。默认：2000「末」\n<<<[END_TOOL_REQUEST]>>>\n\n功能：高级数据抓取器，支持复杂的数据提取。"}]}}