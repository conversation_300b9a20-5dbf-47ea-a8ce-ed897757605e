const fs = require('fs');

class SmartHealthManager {
    constructor() {
        this.config = {};
        this.debugMode = false;
        this.healthData = new Map();
        this.medicationReminders = new Map();
    }

    log(message) {
        if (this.debugMode) {
            console.error(`[SmartHealthManager] ${message}`);
        }
    }

    loadConfig(configData) {
        try {
            let config = {};
            if (configData && configData.trim()) {
                try {
                    config = JSON.parse(configData);
                } catch (parseError) {
                    this.log(`Failed to parse config: ${parseError.message}`);
                }
            }

            this.config = config;
            this.debugMode = this.config.DEBUG_MODE === 'true' || this.config.DEBUG_MODE === true;
            
            this.log('Configuration loaded successfully');
        } catch (error) {
            throw new Error(`Failed to load configuration: ${error.message}`);
        }
    }

    analyzeFitness(activityType, duration, intensity = 'moderate', heartRate = null, caloriesBurned = null, distance = null, date = null) {
        const fitnessData = {
            id: this.generateId(),
            activityType: activityType,
            duration: parseInt(duration),
            intensity: intensity,
            heartRate: heartRate ? parseInt(heartRate) : null,
            caloriesBurned: caloriesBurned ? parseInt(caloriesBurned) : null,
            distance: distance ? parseFloat(distance) : null,
            date: date || new Date().toISOString().split('T')[0],
            timestamp: new Date().toISOString(),
            analysis: this.calculateFitnessMetrics(activityType, duration, intensity, heartRate)
        };

        this.healthData.set(fitnessData.id, fitnessData);

        let report = `🏃‍♂️ 运动数据分析报告\n\n`;
        report += `🏃 运动类型: ${activityType}\n`;
        report += `⏱️ 运动时长: ${duration} 分钟\n`;
        report += `💪 运动强度: ${intensity}\n`;
        if (heartRate) report += `❤️ 平均心率: ${heartRate} bpm\n`;
        if (caloriesBurned) report += `🔥 消耗卡路里: ${caloriesBurned} kcal\n`;
        if (distance) report += `📏 运动距离: ${distance} 公里\n`;
        report += `📅 运动日期: ${fitnessData.date}\n\n`;

        report += `📊 分析结果:\n`;
        report += `• 运动效果评分: ${fitnessData.analysis.effectivenessScore}/10\n`;
        report += `• 心率区间: ${fitnessData.analysis.heartRateZone}\n`;
        report += `• 建议恢复时间: ${fitnessData.analysis.recoveryTime}\n`;
        report += `• 下次运动建议: ${fitnessData.analysis.nextWorkoutSuggestion}\n\n`;

        report += `💡 个性化建议:\n`;
        fitnessData.analysis.recommendations.forEach(rec => {
            report += `• ${rec}\n`;
        });

        return report;
    }

    trackNutrition(mealType, foods, date = null, goals = 'maintenance') {
        const nutritionData = {
            id: this.generateId(),
            mealType: mealType,
            foods: this.parseFoodData(foods),
            date: date || new Date().toISOString().split('T')[0],
            goals: goals,
            timestamp: new Date().toISOString(),
            analysis: null
        };

        nutritionData.analysis = this.calculateNutritionMetrics(nutritionData.foods, goals);
        this.healthData.set(nutritionData.id, nutritionData);

        let report = `🍽️ 营养摄入分析报告\n\n`;
        report += `🍽️ 餐次: ${mealType}\n`;
        report += `📅 日期: ${nutritionData.date}\n`;
        report += `🎯 营养目标: ${goals}\n\n`;

        report += `📋 食物清单:\n`;
        nutritionData.foods.forEach(food => {
            report += `• ${food.name}: ${food.amount}${food.unit}\n`;
        });

        report += `\n📊 营养分析:\n`;
        report += `🔥 总卡路里: ${nutritionData.analysis.totalCalories} kcal\n`;
        report += `🍞 碳水化合物: ${nutritionData.analysis.carbs}g\n`;
        report += `🥩 蛋白质: ${nutritionData.analysis.protein}g\n`;
        report += `🥑 脂肪: ${nutritionData.analysis.fat}g\n`;
        report += `🧂 钠: ${nutritionData.analysis.sodium}mg\n`;
        report += `🍃 纤维: ${nutritionData.analysis.fiber}g\n\n`;

        report += `📈 营养评估:\n`;
        report += `• 营养均衡度: ${nutritionData.analysis.balanceScore}/10\n`;
        report += `• 目标达成度: ${nutritionData.analysis.goalAchievement}%\n\n`;

        report += `💡 营养建议:\n`;
        nutritionData.analysis.recommendations.forEach(rec => {
            report += `• ${rec}\n`;
        });

        return report;
    }

    analyzeSleep(sleepDuration, bedtime = null, wakeTime = null, sleepQuality = null, deepSleep = null, remSleep = null, date = null) {
        const sleepData = {
            id: this.generateId(),
            sleepDuration: parseFloat(sleepDuration),
            bedtime: bedtime,
            wakeTime: wakeTime,
            sleepQuality: sleepQuality ? parseInt(sleepQuality) : null,
            deepSleep: deepSleep ? parseFloat(deepSleep) : null,
            remSleep: remSleep ? parseFloat(remSleep) : null,
            date: date || new Date().toISOString().split('T')[0],
            timestamp: new Date().toISOString(),
            analysis: null
        };

        sleepData.analysis = this.calculateSleepMetrics(sleepData);
        this.healthData.set(sleepData.id, sleepData);

        let report = `😴 睡眠质量分析报告\n\n`;
        report += `⏰ 睡眠时长: ${sleepDuration} 小时\n`;
        if (bedtime) report += `🌙 就寝时间: ${bedtime}\n`;
        if (wakeTime) report += `🌅 起床时间: ${wakeTime}\n`;
        if (sleepQuality) report += `💤 睡眠质量评分: ${sleepQuality}/10\n`;
        if (deepSleep) report += `🛌 深度睡眠: ${deepSleep} 小时\n`;
        if (remSleep) report += `🧠 REM睡眠: ${remSleep} 小时\n`;
        report += `📅 睡眠日期: ${sleepData.date}\n\n`;

        report += `📊 睡眠分析:\n`;
        report += `• 睡眠充足度: ${sleepData.analysis.adequacyScore}/10\n`;
        report += `• 睡眠效率: ${sleepData.analysis.efficiency}%\n`;
        report += `• 睡眠阶段分布: ${sleepData.analysis.stageDistribution}\n`;
        report += `• 整体睡眠评分: ${sleepData.analysis.overallScore}/10\n\n`;

        report += `💡 睡眠改善建议:\n`;
        sleepData.analysis.recommendations.forEach(rec => {
            report += `• ${rec}\n`;
        });

        return report;
    }

    assessHealthRisk(age, gender, height, weight, bloodPressure = null, cholesterol = null, smoking = 'no', familyHistory = '', exerciseFrequency = 'weekly') {
        const healthProfile = {
            id: this.generateId(),
            age: parseInt(age),
            gender: gender,
            height: parseInt(height),
            weight: parseInt(weight),
            bmi: this.calculateBMI(weight, height),
            bloodPressure: bloodPressure,
            cholesterol: cholesterol,
            smoking: smoking,
            familyHistory: familyHistory,
            exerciseFrequency: exerciseFrequency,
            timestamp: new Date().toISOString(),
            riskAssessment: null
        };

        healthProfile.riskAssessment = this.calculateHealthRisks(healthProfile);

        let report = `🏥 健康风险评估报告\n\n`;
        report += `👤 基本信息:\n`;
        report += `• 年龄: ${age} 岁\n`;
        report += `• 性别: ${gender === 'male' ? '男' : '女'}\n`;
        report += `• 身高: ${height} cm\n`;
        report += `• 体重: ${weight} kg\n`;
        report += `• BMI: ${healthProfile.bmi.toFixed(1)} (${this.getBMICategory(healthProfile.bmi)})\n`;
        if (bloodPressure) report += `• 血压: ${bloodPressure} mmHg\n`;
        if (cholesterol) report += `• 胆固醇: ${cholesterol} mg/dL\n`;
        report += `• 吸烟状况: ${smoking === 'yes' ? '是' : '否'}\n`;
        report += `• 运动频率: ${exerciseFrequency}\n\n`;

        report += `⚠️ 风险评估:\n`;
        report += `• 整体健康风险: ${healthProfile.riskAssessment.overallRisk}\n`;
        report += `• 心血管疾病风险: ${healthProfile.riskAssessment.cardiovascularRisk}\n`;
        report += `• 糖尿病风险: ${healthProfile.riskAssessment.diabetesRisk}\n`;
        report += `• 肥胖相关风险: ${healthProfile.riskAssessment.obesityRisk}\n\n`;

        if (healthProfile.riskAssessment.riskFactors.length > 0) {
            report += `🚨 主要风险因素:\n`;
            healthProfile.riskAssessment.riskFactors.forEach(factor => {
                report += `• ${factor}\n`;
            });
            report += `\n`;
        }

        report += `💡 健康改善建议:\n`;
        healthProfile.riskAssessment.recommendations.forEach(rec => {
            report += `• ${rec}\n`;
        });

        return report;
    }

    createWorkoutPlan(goal, fitnessLevel, availableTime, equipment = 'gym', preferences = '', limitations = '') {
        const workoutPlan = {
            id: this.generateId(),
            goal: goal,
            fitnessLevel: fitnessLevel,
            availableTime: parseInt(availableTime),
            equipment: equipment,
            preferences: preferences,
            limitations: limitations,
            createdAt: new Date().toISOString(),
            plan: this.generateWorkoutSchedule(goal, fitnessLevel, availableTime, equipment, limitations)
        };

        let report = `💪 个性化运动计划\n\n`;
        report += `🎯 健身目标: ${goal}\n`;
        report += `📊 健身水平: ${fitnessLevel}\n`;
        report += `⏰ 可用时间: ${availableTime} 分钟/天\n`;
        report += `🏋️ 器械条件: ${equipment}\n`;
        if (preferences) report += `💡 运动偏好: ${preferences}\n`;
        if (limitations) report += `⚠️ 身体限制: ${limitations}\n`;
        report += `📅 创建时间: ${workoutPlan.createdAt}\n\n`;

        report += `📋 训练计划:\n`;
        workoutPlan.plan.schedule.forEach((day, index) => {
            report += `${day.day}: ${day.focus}\n`;
            day.exercises.forEach(exercise => {
                report += `  • ${exercise.name}: ${exercise.sets}组 x ${exercise.reps}次`;
                if (exercise.rest) report += ` (休息${exercise.rest}秒)`;
                report += `\n`;
            });
            report += `\n`;
        });

        report += `📝 训练要点:\n`;
        workoutPlan.plan.tips.forEach(tip => {
            report += `• ${tip}\n`;
        });

        return report;
    }

    generateHealthReport(timePeriod, includeFitness = true, includeNutrition = true, includeSleep = true, includeRecommendations = true) {
        const reportData = {
            id: this.generateId(),
            timePeriod: timePeriod,
            generatedAt: new Date().toISOString(),
            summary: this.generateHealthSummary(timePeriod, includeFitness, includeNutrition, includeSleep)
        };

        let report = `📊 综合健康报告 (${timePeriod})\n\n`;
        report += `📅 报告生成时间: ${reportData.generatedAt}\n`;
        report += `📈 报告周期: ${timePeriod}\n\n`;

        if (includeFitness) {
            report += `🏃‍♂️ 运动健身总结:\n`;
            report += `• 总运动次数: ${reportData.summary.fitness.totalWorkouts}\n`;
            report += `• 总运动时长: ${reportData.summary.fitness.totalDuration} 分钟\n`;
            report += `• 平均运动强度: ${reportData.summary.fitness.averageIntensity}\n`;
            report += `• 最常进行的运动: ${reportData.summary.fitness.favoriteActivity}\n\n`;
        }

        if (includeNutrition) {
            report += `🍽️ 营养摄入总结:\n`;
            report += `• 平均每日卡路里: ${reportData.summary.nutrition.averageCalories} kcal\n`;
            report += `• 营养均衡度: ${reportData.summary.nutrition.balanceScore}/10\n`;
            report += `• 主要营养来源: ${reportData.summary.nutrition.mainSources}\n\n`;
        }

        if (includeSleep) {
            report += `😴 睡眠质量总结:\n`;
            report += `• 平均睡眠时长: ${reportData.summary.sleep.averageDuration} 小时\n`;
            report += `• 平均睡眠质量: ${reportData.summary.sleep.averageQuality}/10\n`;
            report += `• 睡眠规律性: ${reportData.summary.sleep.regularity}\n\n`;
        }

        if (includeRecommendations) {
            report += `💡 综合健康建议:\n`;
            reportData.summary.recommendations.forEach(rec => {
                report += `• ${rec}\n`;
            });
        }

        return report;
    }

    medicationReminder(medicationName, dosage, frequency, startDate, endDate = null, reminderTimes = [], notes = '') {
        const reminderId = this.generateId();
        const reminder = {
            id: reminderId,
            medicationName: medicationName,
            dosage: dosage,
            frequency: frequency,
            startDate: startDate,
            endDate: endDate,
            reminderTimes: Array.isArray(reminderTimes) ? reminderTimes : reminderTimes.split(',').map(t => t.trim()),
            notes: notes,
            createdAt: new Date().toISOString(),
            isActive: true
        };

        this.medicationReminders.set(reminderId, reminder);

        let report = `💊 用药提醒设置成功\n\n`;
        report += `💊 药物名称: ${medicationName}\n`;
        report += `📏 剂量: ${dosage}\n`;
        report += `🔄 服用频率: ${frequency}\n`;
        report += `📅 开始日期: ${startDate}\n`;
        if (endDate) report += `📅 结束日期: ${endDate}\n`;
        if (reminderTimes.length > 0) {
            report += `⏰ 提醒时间: ${reminder.reminderTimes.join(', ')}\n`;
        }
        if (notes) report += `📝 特殊说明: ${notes}\n`;
        report += `🆔 提醒ID: ${reminderId}\n`;
        report += `📅 创建时间: ${reminder.createdAt}\n\n`;

        report += `✅ 提醒已激活，将按设定时间提醒您服药\n`;
        report += `💡 建议在服药后记录，以便跟踪用药效果`;

        return report;
    }

    // 辅助计算方法
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    calculateBMI(weight, height) {
        const heightInMeters = height / 100;
        return weight / (heightInMeters * heightInMeters);
    }

    getBMICategory(bmi) {
        if (bmi < 18.5) return '偏瘦';
        if (bmi < 24) return '正常';
        if (bmi < 28) return '超重';
        return '肥胖';
    }

    calculateFitnessMetrics(activityType, duration, intensity, heartRate) {
        const baseScore = duration >= 30 ? 8 : duration >= 15 ? 6 : 4;
        const intensityMultiplier = intensity === 'high' ? 1.2 : intensity === 'low' ? 0.8 : 1.0;
        const effectivenessScore = Math.min(10, Math.round(baseScore * intensityMultiplier));

        const heartRateZone = heartRate ? 
            (heartRate > 150 ? '高强度区间' : heartRate > 120 ? '中等强度区间' : '低强度区间') : 
            '未监测';

        const recoveryTime = intensity === 'high' ? '24-48小时' : intensity === 'moderate' ? '12-24小时' : '6-12小时';

        const recommendations = [
            `本次${activityType}运动效果良好`,
            `建议保持当前运动强度`,
            '运动后注意适当补水和拉伸'
        ];

        return {
            effectivenessScore,
            heartRateZone,
            recoveryTime,
            nextWorkoutSuggestion: '建议明天进行轻度恢复性运动',
            recommendations
        };
    }

    parseFoodData(foods) {
        const foodList = [];
        const foodEntries = foods.split(';');
        
        foodEntries.forEach(entry => {
            const parts = entry.split(',');
            if (parts.length >= 3) {
                foodList.push({
                    name: parts[0].trim(),
                    amount: parseFloat(parts[1].trim()),
                    unit: parts[2].trim()
                });
            }
        });

        return foodList;
    }

    calculateNutritionMetrics(foods, goals) {
        // 简化的营养计算
        const totalCalories = foods.length * 150; // 简化计算
        const carbs = Math.round(totalCalories * 0.5 / 4);
        const protein = Math.round(totalCalories * 0.2 / 4);
        const fat = Math.round(totalCalories * 0.3 / 9);

        return {
            totalCalories,
            carbs,
            protein,
            fat,
            sodium: 800,
            fiber: 15,
            balanceScore: 8,
            goalAchievement: 85,
            recommendations: [
                '营养搭配较为均衡',
                '建议增加蔬菜摄入',
                '注意控制钠的摄入量'
            ]
        };
    }

    calculateSleepMetrics(sleepData) {
        const adequacyScore = sleepData.sleepDuration >= 7 ? 9 : sleepData.sleepDuration >= 6 ? 7 : 5;
        const efficiency = sleepData.sleepQuality ? sleepData.sleepQuality * 10 : 80;

        return {
            adequacyScore,
            efficiency,
            stageDistribution: '深度睡眠30%, REM睡眠25%, 浅睡眠45%',
            overallScore: Math.round((adequacyScore + efficiency / 10) / 2),
            recommendations: [
                sleepData.sleepDuration < 7 ? '建议增加睡眠时间至7-9小时' : '睡眠时长良好',
                '保持规律的作息时间',
                '睡前避免使用电子设备'
            ]
        };
    }

    calculateHealthRisks(profile) {
        const riskFactors = [];
        const recommendations = [];

        // BMI风险评估
        if (profile.bmi >= 28) {
            riskFactors.push('BMI超标，存在肥胖风险');
            recommendations.push('建议控制体重，目标减重5-10%');
        }

        // 年龄风险
        if (profile.age > 45) {
            riskFactors.push('年龄相关的健康风险增加');
            recommendations.push('建议定期进行健康体检');
        }

        // 吸烟风险
        if (profile.smoking === 'yes') {
            riskFactors.push('吸烟显著增加多种疾病风险');
            recommendations.push('强烈建议戒烟');
        }

        const overallRisk = riskFactors.length >= 3 ? '高风险' : riskFactors.length >= 1 ? '中等风险' : '低风险';

        return {
            overallRisk,
            cardiovascularRisk: profile.bmi > 25 || profile.smoking === 'yes' ? '中等风险' : '低风险',
            diabetesRisk: profile.bmi > 28 ? '中等风险' : '低风险',
            obesityRisk: profile.bmi >= 28 ? '高风险' : profile.bmi >= 24 ? '中等风险' : '低风险',
            riskFactors,
            recommendations: recommendations.length > 0 ? recommendations : ['继续保持健康的生活方式']
        };
    }

    generateWorkoutSchedule(goal, fitnessLevel, availableTime, equipment, limitations) {
        const schedule = [];
        const exercises = this.getExercisesByGoal(goal, equipment, limitations);

        // 简化的训练计划生成
        const daysPerWeek = availableTime >= 60 ? 5 : availableTime >= 45 ? 4 : 3;
        
        for (let i = 0; i < daysPerWeek; i++) {
            const dayExercises = exercises.slice(i * 2, (i + 1) * 2);
            schedule.push({
                day: `第${i + 1}天`,
                focus: goal === 'muscle_gain' ? '力量训练' : goal === 'weight_loss' ? '有氧训练' : '综合训练',
                exercises: dayExercises
            });
        }

        return {
            schedule,
            tips: [
                '训练前进行5-10分钟热身',
                '训练后进行拉伸放松',
                '保持正确的动作姿势',
                '根据身体状况调整训练强度'
            ]
        };
    }

    getExercisesByGoal(goal, equipment, limitations) {
        const baseExercises = [
            { name: '深蹲', sets: 3, reps: '12-15', rest: 60 },
            { name: '俯卧撑', sets: 3, reps: '10-12', rest: 60 },
            { name: '平板支撑', sets: 3, reps: '30-60秒', rest: 60 },
            { name: '卷腹', sets: 3, reps: '15-20', rest: 45 }
        ];

        if (equipment === 'gym') {
            baseExercises.push(
                { name: '杠铃卧推', sets: 3, reps: '8-10', rest: 90 },
                { name: '引体向上', sets: 3, reps: '6-8', rest: 90 }
            );
        }

        return baseExercises;
    }

    generateHealthSummary(timePeriod, includeFitness, includeNutrition, includeSleep) {
        // 模拟数据生成
        return {
            fitness: {
                totalWorkouts: 12,
                totalDuration: 720,
                averageIntensity: '中等',
                favoriteActivity: '跑步'
            },
            nutrition: {
                averageCalories: 2000,
                balanceScore: 7.5,
                mainSources: '谷物、蛋白质、蔬菜'
            },
            sleep: {
                averageDuration: 7.2,
                averageQuality: 7.8,
                regularity: '良好'
            },
            recommendations: [
                '整体健康状况良好',
                '建议增加力量训练',
                '保持当前的饮食习惯',
                '继续维持良好的睡眠质量'
            ]
        };
    }

    async processRequest(params) {
        const { command } = params;
        
        switch (command) {
            case 'analyze_fitness':
                return this.analyzeFitness(
                    params.activity_type,
                    params.duration,
                    params.intensity,
                    params.heart_rate,
                    params.calories_burned,
                    params.distance,
                    params.date
                );

            case 'track_nutrition':
                return this.trackNutrition(
                    params.meal_type,
                    params.foods,
                    params.date,
                    params.goals
                );

            case 'analyze_sleep':
                return this.analyzeSleep(
                    params.sleep_duration,
                    params.bedtime,
                    params.wake_time,
                    params.sleep_quality,
                    params.deep_sleep,
                    params.rem_sleep,
                    params.date
                );

            case 'assess_health_risk':
                return this.assessHealthRisk(
                    params.age,
                    params.gender,
                    params.height,
                    params.weight,
                    params.blood_pressure,
                    params.cholesterol,
                    params.smoking,
                    params.family_history,
                    params.exercise_frequency
                );

            case 'create_workout_plan':
                return this.createWorkoutPlan(
                    params.goal,
                    params.fitness_level,
                    params.available_time,
                    params.equipment,
                    params.preferences,
                    params.limitations
                );

            case 'generate_health_report':
                return this.generateHealthReport(
                    params.time_period,
                    params.include_fitness !== 'false',
                    params.include_nutrition !== 'false',
                    params.include_sleep !== 'false',
                    params.include_recommendations !== 'false'
                );

            case 'medication_reminder':
                return this.medicationReminder(
                    params.medication_name,
                    params.dosage,
                    params.frequency,
                    params.start_date,
                    params.end_date,
                    params.reminder_times,
                    params.notes
                );

            default:
                throw new Error(`未知命令: ${command}`);
        }
    }
}

// 主执行逻辑
async function main() {
    const manager = new SmartHealthManager();
    
    try {
        let inputData = '';
        process.stdin.setEncoding('utf8');
        
        for await (const chunk of process.stdin) {
            inputData += chunk;
        }
        
        if (!inputData.trim()) {
            throw new Error('No input data received');
        }

        const params = JSON.parse(inputData.trim());
        
        manager.loadConfig(JSON.stringify(params.config || {}));
        
        const result = await manager.processRequest(params);
        
        const response = {
            status: 'success',
            result: result
        };

        console.log(JSON.stringify(response));
        
    } catch (error) {
        const response = {
            status: 'error',
            error: error.message,
            messageForAI: `智能健康管理操作失败：${error.message}`
        };

        console.log(JSON.stringify(response));
        process.exit(1);
    }
}

main().catch(error => {
    console.error('Unexpected error:', error);
    process.exit(1);
});
