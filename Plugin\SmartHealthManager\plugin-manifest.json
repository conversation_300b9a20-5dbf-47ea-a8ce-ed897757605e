{"manifestVersion": "1.0.0", "name": "SmartHealthManager", "displayName": "智能健康管理系统", "version": "1.0.0", "description": "AI驱动的全方位健康管理系统，支持运动数据分析、饮食营养跟踪、睡眠质量监测、健康风险评估、个性化建议生成等功能。集成可穿戴设备数据，提供专业级健康指导。", "author": "VCP Health", "pluginType": "synchronous", "entryPoint": {"type": "nodejs", "command": "node SmartHealthManager.js"}, "communication": {"protocol": "stdio", "timeout": 120000}, "configSchema": {"DebugMode": {"type": "boolean", "description": "是否启用详细的调试日志输出到stderr", "default": false, "required": false}}, "capabilities": {"invocationCommands": [{"command": "analyze_fitness", "description": "分析运动和健身数据。\n\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」SmartHealthManager「末」,\ncommand:「始」analyze_fitness「末」,\nactivity_type:「始」(必需) 运动类型：running、cycling、swimming、weightlifting、yoga等「末」,\nduration:「始」(必需) 运动时长（分钟）「末」,\nintensity:「始」(可选) 运动强度：low、moderate、high。默认：moderate「末」,\nheart_rate:「始」(可选) 平均心率「末」,\ncalories_burned:「始」(可选) 消耗卡路里「末」,\ndistance:「始」(可选) 运动距离（公里）「末」,\ndate:「始」(可选) 运动日期，格式：YYYY-MM-DD「末」\n<<<[END_TOOL_REQUEST]>>>\n\n功能：分析运动数据，评估运动效果，提供个性化的健身建议。"}, {"command": "track_nutrition", "description": "跟踪和分析饮食营养。\n\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」SmartHealthManager「末」,\ncommand:「始」track_nutrition「末」,\nmeal_type:「始」(必需) 餐次：breakfast、lunch、dinner、snack「末」,\nfoods:「始」(必需) 食物列表，用分号分隔，格式：\"食物名称,数量,单位\"「末」,\ndate:「始」(可选) 日期，格式：YYYY-MM-DD「末」,\ngoals:「始」(可选) 营养目标：weight_loss、muscle_gain、maintenance。默认：maintenance「末」\n<<<[END_TOOL_REQUEST]>>>\n\n功能：跟踪营养摄入，分析营养均衡度，提供饮食改善建议。"}, {"commandIdentifier": "AnalyzeSleep", "description": "分析睡眠质量和模式。\n\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」SmartHealthManager「末」,\naction:「始」analyze_sleep「末」,\nsleep_duration:「始」(必需) 睡眠时长（小时）「末」,\nbedtime:「始」(可选) 就寝时间，格式：HH:MM「末」,\nwake_time:「始」(可选) 起床时间，格式：HH:MM「末」,\nsleep_quality:「始」(可选) 睡眠质量评分：1-10「末」,\ndeep_sleep:「始」(可选) 深度睡眠时长（小时）「末」,\nrem_sleep:「始」(可选) REM睡眠时长（小时）「末」,\ndate:「始」(可选) 日期，格式：YYYY-MM-DD「末」\n<<<[END_TOOL_REQUEST]>>>", "example": "```text\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」SmartHealthManager「末」,\naction:「始」analyze_sleep「末」,\nsleep_duration:「始」7.5「末」,\nbedtime:「始」23:30「末」,\nwake_time:「始」07:00「末」,\nsleep_quality:「始」8「末」\n<<<[END_TOOL_REQUEST]>>>\n```"}, {"commandIdentifier": "AssessHealthRisk", "description": "评估健康风险和提供预防建议。\n\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」SmartHealthManager「末」,\naction:「始」assess_health_risk「末」,\nage:「始」(必需) 年龄「末」,\ngender:「始」(必需) 性别：male、female「末」,\nheight:「始」(必需) 身高（厘米）「末」,\nweight:「始」(必需) 体重（公斤）「末」,\nblood_pressure:「始」(可选) 血压，格式：\"收缩压/舒张压\"「末」,\ncholesterol:「始」(可选) 胆固醇水平「末」,\nsmoking:「始」(可选) 是否吸烟：yes、no「末」,\nfamily_history:「始」(可选) 家族病史「末」,\nexercise_frequency:「始」(可选) 运动频率：daily、weekly、monthly、rarely「末」\n<<<[END_TOOL_REQUEST]>>>", "example": "```text\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」SmartHealthManager「末」,\naction:「始」assess_health_risk「末」,\nage:「始」35「末」,\ngender:「始」male「末」,\nheight:「始」175「末」,\nweight:「始」80「末」,\nblood_pressure:「始」130/85「末」,\nsmoking:「始」no「末」,\nexercise_frequency:「始」weekly「末」\n<<<[END_TOOL_REQUEST]>>>\n```"}, {"commandIdentifier": "CreateWorkoutPlan", "description": "创建个性化运动计划。\n\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」SmartHealthManager「末」,\naction:「始」create_workout_plan「末」,\ngoal:「始」(必需) 健身目标：weight_loss、muscle_gain、endurance、strength、flexibility「末」,\nfitness_level:「始」(必需) 健身水平：beginner、intermediate、advanced「末」,\navailable_time:「始」(必需) 可用时间（分钟/天）「末」,\nequipment:「始」(可选) 可用器械：gym、home、bodyweight、resistance_bands等「末」,\npreferences:「始」(可选) 运动偏好「末」,\nlimitations:「始」(可选) 身体限制或伤病「末」\n<<<[END_TOOL_REQUEST]>>>", "example": "```text\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」SmartHealthManager「末」,\naction:「始」create_workout_plan「末」,\ngoal:「始」muscle_gain「末」,\nfitness_level:「始」intermediate「末」,\navailable_time:「始」60「末」,\nequipment:「始」gym「末」,\nlimitations:「始」左膝轻微伤病，避免高冲击运动「末」\n<<<[END_TOOL_REQUEST]>>>\n```"}, {"commandIdentifier": "GenerateHealthReport", "description": "生成综合健康报告。\n\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」SmartHealthManager「末」,\naction:「始」generate_health_report「末」,\ntime_period:「始」(必需) 报告周期：weekly、monthly、quarterly「末」,\ninclude_fitness:「始」(可选) 包含健身数据：true、false。默认：true「末」,\ninclude_nutrition:「始」(可选) 包含营养数据：true、false。默认：true「末」,\ninclude_sleep:「始」(可选) 包含睡眠数据：true、false。默认：true「末」,\ninclude_recommendations:「始」(可选) 包含改善建议：true、false。默认：true「末」\n<<<[END_TOOL_REQUEST]>>>", "example": "```text\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」SmartHealthManager「末」,\naction:「始」generate_health_report「末」,\ntime_period:「始」monthly「末」,\ninclude_fitness:「始」true「末」,\ninclude_nutrition:「始」true「末」,\ninclude_sleep:「始」true「末」\n<<<[END_TOOL_REQUEST]>>>\n```"}, {"commandIdentifier": "MedicationReminder", "description": "药物提醒和管理。\n\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」SmartHealthManager「末」,\naction:「始」medication_reminder「末」,\nmedication_name:「始」(必需) 药物名称「末」,\ndosage:「始」(必需) 剂量「末」,\nfrequency:「始」(必需) 服用频率：daily、twice_daily、three_times_daily、weekly等「末」,\nstart_date:「始」(必需) 开始日期，格式：YYYY-MM-DD「末」,\nend_date:「始」(可选) 结束日期，格式：YYYY-MM-DD「末」,\nreminder_times:「始」(可选) 提醒时间，用逗号分隔，格式：HH:MM「末」,\nnotes:「始」(可选) 特殊说明「末」\n<<<[END_TOOL_REQUEST]>>>", "example": "```text\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」SmartHealthManager「末」,\naction:「始」medication_reminder「末」,\nmedication_name:「始」维生素D3「末」,\ndosage:「始」1000IU「末」,\nfrequency:「始」daily「末」,\nstart_date:「始」2025-01-27「末」,\nreminder_times:「始」08:00「末」,\nnotes:「始」随餐服用「末」\n<<<[END_TOOL_REQUEST]>>>\n```"}]}}