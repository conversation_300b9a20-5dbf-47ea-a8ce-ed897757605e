#!/usr/bin/env node

/**
 * 即梦AI插件测试文件
 * 用于测试插件的各项功能
 */

const { spawn } = require('child_process');
const path = require('path');

// 测试数据
const testCases = [
    {
        name: "图片生成测试",
        input: {
            type: "image",
            prompt: "一只可爱的小猫在花园里玩耍，阳光明媚，高清摄影风格",
            quality: "hd",
            style: "realistic",
            aspect_ratio: "16:9"
        }
    },
    {
        name: "视频生成测试",
        input: {
            type: "video",
            prompt: "海边日落，海浪轻拍沙滩，镜头缓慢推进",
            duration: 10,
            motion: "zoom_in",
            quality: "hd"
        }
    },
    {
        name: "数字人生成测试",
        input: {
            type: "digital_human",
            character: "年轻女性，长发，职业装，亲和力强",
            action: "挥手打招呼",
            expression: "微笑",
            background: "现代办公室"
        }
    }
];

// 运行单个测试
function runTest(testCase) {
    return new Promise((resolve, reject) => {
        console.log(`\n🧪 开始测试: ${testCase.name}`);
        console.log(`📝 输入参数:`, JSON.stringify(testCase.input, null, 2));

        const pluginPath = path.join(__dirname, 'JimengAI.js');
        const child = spawn('node', [pluginPath], {
            stdio: ['pipe', 'pipe', 'pipe']
        });

        let output = '';
        let error = '';

        child.stdout.on('data', (data) => {
            output += data.toString();
        });

        child.stderr.on('data', (data) => {
            error += data.toString();
        });

        child.on('close', (code) => {
            console.log(`📤 输出结果:`);
            
            if (output) {
                try {
                    // 尝试从输出中提取JSON部分
                    const lines = output.split('\n');
                    let jsonStart = -1;
                    let jsonEnd = -1;

                    for (let i = 0; i < lines.length; i++) {
                        if (lines[i].trim().startsWith('{')) {
                            jsonStart = i;
                            break;
                        }
                    }

                    for (let i = lines.length - 1; i >= 0; i--) {
                        if (lines[i].trim().endsWith('}')) {
                            jsonEnd = i;
                            break;
                        }
                    }

                    if (jsonStart !== -1 && jsonEnd !== -1) {
                        const jsonStr = lines.slice(jsonStart, jsonEnd + 1).join('\n');
                        const result = JSON.parse(jsonStr);
                        console.log(JSON.stringify(result, null, 2));

                        if (result.status === 'success') {
                            console.log(`✅ 测试通过: ${testCase.name}`);
                        } else {
                            console.log(`❌ 测试失败: ${testCase.name} - ${result.error}`);
                        }
                    } else {
                        console.log(output);
                        console.log(`⚠️  无法找到有效的JSON输出`);
                    }
                } catch (e) {
                    console.log(output);
                    console.log(`⚠️  JSON解析错误: ${e.message}`);
                }
            }

            if (error) {
                console.log(`🔍 错误信息:`);
                console.log(error);
            }

            console.log(`🏁 进程退出码: ${code}`);
            resolve({ testCase, code, output, error });
        });

        child.on('error', (err) => {
            console.log(`❌ 进程启动失败: ${err.message}`);
            reject(err);
        });

        // 发送测试数据
        child.stdin.write(JSON.stringify(testCase.input));
        child.stdin.end();
    });
}

// 运行所有测试
async function runAllTests() {
    console.log('🚀 即梦AI插件测试开始');
    console.log('=' * 50);

    const results = [];

    for (const testCase of testCases) {
        try {
            const result = await runTest(testCase);
            results.push(result);
            
            // 测试间隔
            await new Promise(resolve => setTimeout(resolve, 2000));
        } catch (error) {
            console.log(`❌ 测试失败: ${testCase.name} - ${error.message}`);
            results.push({ testCase, error });
        }
    }

    // 输出测试总结
    console.log('\n' + '=' * 50);
    console.log('📊 测试总结:');
    
    let passCount = 0;
    let failCount = 0;

    results.forEach((result, index) => {
        const testName = result.testCase.name;
        if (result.code === 0 && result.output) {
            try {
                // 尝试从输出中提取JSON部分
                const lines = result.output.split('\n');
                let jsonStart = -1;
                let jsonEnd = -1;

                for (let i = 0; i < lines.length; i++) {
                    if (lines[i].trim().startsWith('{')) {
                        jsonStart = i;
                        break;
                    }
                }

                for (let i = lines.length - 1; i >= 0; i--) {
                    if (lines[i].trim().endsWith('}')) {
                        jsonEnd = i;
                        break;
                    }
                }

                if (jsonStart !== -1 && jsonEnd !== -1) {
                    const jsonStr = lines.slice(jsonStart, jsonEnd + 1).join('\n');
                    const output = JSON.parse(jsonStr);
                    if (output.status === 'success') {
                        console.log(`✅ ${index + 1}. ${testName} - 通过`);
                        passCount++;
                    } else {
                        console.log(`❌ ${index + 1}. ${testName} - 失败: ${output.error}`);
                        failCount++;
                    }
                } else {
                    console.log(`⚠️  ${index + 1}. ${testName} - 无法找到JSON输出`);
                    failCount++;
                }
            } catch (e) {
                console.log(`⚠️  ${index + 1}. ${testName} - JSON解析错误`);
                failCount++;
            }
        } else {
            console.log(`❌ ${index + 1}. ${testName} - 执行失败`);
            failCount++;
        }
    });

    console.log(`\n📈 测试结果: ${passCount} 通过, ${failCount} 失败, 总计 ${results.length} 个测试`);
    
    if (failCount === 0) {
        console.log('🎉 所有测试通过！');
    } else {
        console.log('⚠️  部分测试失败，请检查配置和网络连接');
    }
}

// 检查配置文件
function checkConfig() {
    const fs = require('fs');
    const configPath = path.join(__dirname, 'config.env');
    
    if (!fs.existsSync(configPath)) {
        console.log('❌ 配置文件不存在！');
        console.log('请复制 config.env.example 为 config.env 并填入正确的配置');
        return false;
    }

    const configContent = fs.readFileSync(configPath, 'utf8');
    if (!configContent.includes('SESSION_ID=') || configContent.includes('SESSION_ID=d9b98e96218328b3af2feb88d31b51460')) {
        console.log('⚠️  请确保在 config.env 中设置了正确的 SESSION_ID');
    }

    return true;
}

// 主函数
async function main() {
    console.log('🔧 检查配置...');
    
    if (!checkConfig()) {
        process.exit(1);
    }

    console.log('✅ 配置检查通过');
    
    await runAllTests();
}

// 运行测试
if (require.main === module) {
    main().catch((error) => {
        console.error('❌ 测试运行失败:', error.message);
        process.exit(1);
    });
}
